# ExportCategories API 参考文档

## 核心 API

### IModelExporter 类

#### 类别排除方法

##### `excludeElementsInCategory(categoryId: Id64String): void`

排除指定类别中的所有元素。

**参数：**
- `categoryId: Id64String` - 要排除的类别 ID

**示例：**
```typescript
exporter.excludeElementsInCategory("0x123456789abcdef0");
```

**源码位置：** `src/IModelExporter.ts:392-394`

```typescript
/** Add a rule to exclude all Elements in a specified Category. */
public excludeElementsInCategory(categoryId: Id64String): void {
  this._excludedElementCategoryIds.add(categoryId);
}
```

#### 元素过滤方法

##### `shouldExportElement(element: Element): boolean`

判断指定元素是否应该被导出，包含类别过滤逻辑。

**参数：**
- `element: Element` - 要检查的元素

**返回值：**
- `boolean` - `true` 表示应该导出，`false` 表示应该跳过

**过滤规则优先级：**
1. 元素 ID 排除检查
2. 类别排除检查（仅对 GeometricElement）
3. 模板模型检查
4. 元素类排除检查
5. 自定义处理器检查

**示例：**
```typescript
const element = sourceDb.elements.getElement(elementId);
if (exporter.shouldExportElement(element)) {
  // 执行导出逻辑
}
```

**源码位置：** `src/IModelExporter.ts:803-835`

#### 导出执行方法

##### `exportAll(): Promise<void>`

导出源 iModel 中的所有实体实例类型。

**注意：** 必须单独调用 `exportSchemas()`。

**示例：**
```typescript
await exporter.exportAll();
```

##### `exportModel(modeledElementId: Id64String): Promise<void>`

导出指定模型的容器、内容和子模型。

**参数：**
- `modeledElementId: Id64String` - 要导出的模型 ID

**示例：**
```typescript
await exporter.exportModel(IModel.repositoryModelId);
```

##### `exportElement(elementId: Id64String): Promise<void>`

导出指定的元素。

**参数：**
- `elementId: Id64String` - 要导出的元素 ID

**示例：**
```typescript
await exporter.exportElement(elementId);
```

### IModelExportHandler 接口

#### 元素过滤钩子

##### `shouldExportElement(element: Element): boolean`

自定义元素过滤逻辑的钩子方法。

**参数：**
- `element: Element` - 要检查的元素

**返回值：**
- `boolean` - `true` 表示应该导出，`false` 表示应该跳过

**默认实现：**
```typescript
public shouldExportElement(_element: Element): boolean {
  return true;
}
```

**自定义实现示例：**
```typescript
class CustomHandler extends IModelExportHandler {
  public override shouldExportElement(element: Element): boolean {
    if (element instanceof GeometricElement) {
      // 只导出特定类别的几何元素
      return this.allowedCategories.has(element.category);
    }
    return super.shouldExportElement(element);
  }
}
```

#### 元素导出钩子

##### `onExportElement(element: Element, isUpdate?: boolean): void`

元素导出时的回调方法。

**参数：**
- `element: Element` - 被导出的元素
- `isUpdate?: boolean` - 可选，表示是否为更新操作

**示例：**
```typescript
public override onExportElement(element: Element, isUpdate?: boolean): void {
  console.log(`Exporting element: ${element.id}, isUpdate: ${isUpdate}`);
  // 自定义导出逻辑
}
```

##### `onSkipElement(elementId: Id64String): void`

元素被跳过时的回调方法。

**参数：**
- `elementId: Id64String` - 被跳过的元素 ID

**示例：**
```typescript
public override onSkipElement(elementId: Id64String): void {
  console.log(`Skipped element: ${elementId}`);
}
```

## 相关类型和接口

### 核心类型

#### `Id64String`

表示 64 位标识符的字符串类型。

```typescript
type Id64String = string;
```

#### `Element`

iModel 中元素的基类。

**重要属性：**
- `id: Id64String` - 元素 ID
- `classFullName: string` - 元素类的完整名称

#### `GeometricElement`

具有几何属性的元素基类。

**重要属性：**
- `category: Id64String` - 元素所属的类别 ID

### 类别相关类型

#### `SpatialCategory`

空间类别元素类。

**静态方法：**
```typescript
static createCode(iModel: IModelDb, scopeElementId: Id64String, codeValue: string): Code
static insert(iModel: IModelDb, definitionModelId: Id64String, name: string, appearance: SubCategoryAppearance): Id64String
```

#### `DrawingCategory`

绘图类别元素类。

#### `CategorySelector`

类别选择器元素类，用于定义视图中显示的类别集合。

**重要属性：**
- `categories: Id64String[]` - 选中的类别 ID 数组

**静态方法：**
```typescript
static insert(iModel: IModelDb, definitionModelId: Id64String, name: string, categories: Id64String[]): Id64String
```

## 工具类和辅助方法

### FilterByViewTransformer

基于视图定义进行过滤的转换器（测试工具类）。

**构造函数：**
```typescript
constructor(
  sourceDb: IModelDb,
  targetDb: IModelDb,
  exportViewDefinitionId: Id64String
)
```

**关键方法：**
```typescript
private excludeCategoriesExcept(exportCategoryIds: Id64Set): void
```

### 常用查询模式

#### 查询所有空间类别

```typescript
const sql = `SELECT ECInstanceId, CodeValue FROM BisCore:SpatialCategory`;
sourceDb.withPreparedStatement(sql, (statement) => {
  while (statement.step() === DbResult.BE_SQLITE_ROW) {
    const categoryId = statement.getValue(0).getId();
    const categoryName = statement.getValue(1).getString();
    // 处理类别
  }
});
```

#### 查询类别使用情况

```typescript
const sql = `
  SELECT Category.Id, COUNT(*) as ElementCount 
  FROM BisCore:GeometricElement 
  GROUP BY Category.Id
`;
```

#### 通过名称查找类别

```typescript
const categoryCode = SpatialCategory.createCode(
  sourceDb,
  IModel.dictionaryId,
  categoryName
);
const categoryId = sourceDb.elements.queryElementIdByCode(categoryCode);
```

## 错误处理

### 常见错误类型

#### `NotFoundError`

当查询不存在的类别时抛出。

```typescript
try {
  const categoryId = sourceDb.elements.queryElementIdByCode(categoryCode);
} catch (error) {
  if (error instanceof NotFoundError) {
    console.warn(`Category not found: ${categoryName}`);
  }
}
```

### 最佳实践

1. **验证类别 ID 存在性**
```typescript
const categoryExists = sourceDb.elements.tryGetElement(categoryId) !== undefined;
if (categoryExists) {
  exporter.excludeElementsInCategory(categoryId);
}
```

2. **批量操作的错误处理**
```typescript
const failedExclusions: string[] = [];
categoryIds.forEach(categoryId => {
  try {
    exporter.excludeElementsInCategory(categoryId);
  } catch (error) {
    failedExclusions.push(categoryId);
    console.error(`Failed to exclude category ${categoryId}:`, error);
  }
});
```

## 性能考虑

### 优化建议

1. **使用 Set 进行快速查找**
```typescript
const allowedCategories = new Set(categoryIds);
// O(1) 查找时间
if (allowedCategories.has(element.category)) {
  // 处理逻辑
}
```

2. **批量数据库操作**
```typescript
// 一次查询获取所有需要的数据
const sql = `SELECT ECInstanceId, Category.Id FROM BisCore:GeometricElement`;
// 而不是多次单独查询
```

3. **延迟排除规则应用**
```typescript
// 收集所有排除规则
const exclusions: string[] = [];
// ... 收集逻辑

// 一次性应用
exclusions.forEach(categoryId => {
  exporter.excludeElementsInCategory(categoryId);
});
```

## 版本兼容性

### API 稳定性

- `excludeElementsInCategory()` - **稳定** (自 v1.0.0)
- `shouldExportElement()` - **稳定** (自 v1.0.0)
- `IModelExportHandler` 接口 - **Beta** (可能有变化)

### 依赖版本

- `@itwin/core-backend` >= 4.0.0
- `@itwin/core-bentley` >= 4.0.0
- `@itwin/core-common` >= 4.0.0

## 相关文档

- [IModelExporter 完整 API 文档](./IModelExporter-API.md)
- [IModelTransformer 使用指南](./IModelTransformer-Guide.md)
- [类别管理最佳实践](./Category-Management-BestPractices.md)
