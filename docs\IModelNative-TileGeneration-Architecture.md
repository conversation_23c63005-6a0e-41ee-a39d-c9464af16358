# iModel Native Tile 生成架构分析

## 概述

基于对 [iTwin/imodel-native](https://github.com/iTwin/imodel-native/tree/2.2.5) 的深入分析，本文档详细描述了 iModel Native 中的 Tile 生成架构，包括瓦片树构建、LOD 管理、几何优化和渲染集成等核心机制。

## Tile 生成架构概览

### 1. 核心组件架构

```cpp
// 基于 imodel-native 架构推断的 Tile 系统组件

namespace Bentley {
namespace DgnPlatform {

//! 瓦片树管理器
struct TileTreeManager {
    // 瓦片树缓存
    std::unordered_map<DgnModelId, std::unique_ptr<TileTree>> m_tileTrees;
    
    // 瓦片生成器
    std::unique_ptr<TileGenerator> m_generator;
    
    // 渲染上下文
    DisplaySystemR m_displaySystem;
    
public:
    // 获取或创建瓦片树
    TileTree* GetOrCreateTileTree(DgnModelId modelId, ViewContextR context);
    
    // 瓦片选择和加载
    void SelectTiles(ViewContextR context, TileSelectionR selection);
    
    // 清理未使用的瓦片
    void PurgeUnusedTiles();
};

//! 瓦片树结构
struct TileTree {
    // 根瓦片
    std::unique_ptr<Tile> m_rootTile;
    
    // 模型引用
    DgnModelId m_modelId;
    
    // 空间范围
    DRange3d m_range;
    
    // LOD 配置
    LevelOfDetailConfig m_lodConfig;
    
    // 瓦片缓存
    TileCache m_cache;
    
public:
    // 瓦片选择算法
    void SelectTiles(ViewContextR context, TileSelectionR selection);
    
    // 获取指定 LOD 的瓦片
    Tile* GetTileAtLOD(DPoint3dCR location, double pixelSize);
    
    // 构建瓦片层次结构
    BentleyStatus BuildHierarchy();
};

//! 单个瓦片
struct Tile {
    // 瓦片标识
    TileId m_id;
    
    // 空间范围
    DRange3d m_range;
    
    // LOD 级别
    uint32_t m_lodLevel;
    
    // 几何内容
    std::unique_ptr<TileContent> m_content;
    
    // 子瓦片
    bvector<std::unique_ptr<Tile>> m_children;
    
    // 父瓦片
    Tile* m_parent;
    
    // 加载状态
    TileLoadState m_loadState;
    
public:
    // 判断是否需要细分
    bool ShouldSubdivide(ViewContextR context) const;
    
    // 异步加载内容
    std::future<BentleyStatus> LoadContentAsync();
    
    // 渲染瓦片
    void Render(RenderContextR context);
    
    // 计算屏幕空间误差
    double ComputeScreenSpaceError(ViewContextR context) const;
};

} // namespace DgnPlatform
} // namespace Bentley
```

### 2. 瓦片生成流程

```cpp
//! 瓦片生成器
class TileGenerator {
private:
    // 几何处理器
    std::unique_ptr<GeometryProcessor> m_geometryProcessor;
    
    // 材质处理器
    std::unique_ptr<MaterialProcessor> m_materialProcessor;
    
    // 压缩器
    std::unique_ptr<TileCompressor> m_compressor;
    
public:
    // 主要生成流程
    BentleyStatus GenerateTiles(DgnModelR model, TileGenerationOptions const& options) {
        // 1. 分析模型几何
        auto geometryAnalysis = AnalyzeModelGeometry(model);
        
        // 2. 计算 LOD 层级
        auto lodLevels = ComputeLODLevels(geometryAnalysis, options);
        
        // 3. 空间分割
        auto spatialPartition = CreateSpatialPartition(model.GetRange(), lodLevels);
        
        // 4. 生成瓦片内容
        for (auto& partition : spatialPartition) {
            GenerateTileContent(model, partition, options);
        }
        
        // 5. 构建瓦片层次结构
        return BuildTileHierarchy(spatialPartition);
    }
    
private:
    // 几何分析
    struct GeometryAnalysis {
        size_t totalTriangles;
        size_t totalVertices;
        DRange3d boundingBox;
        double geometryComplexity;
        bvector<GeometryCluster> clusters;
    };
    
    GeometryAnalysis AnalyzeModelGeometry(DgnModelR model) {
        GeometryAnalysis analysis;
        
        // 遍历模型中的所有几何元素
        auto elementIterator = GeometricElement::MakeIterator(model.GetDgnDb());
        
        for (auto entry : elementIterator) {
            auto element = entry.GetElement<GeometricElement>();
            if (!element.IsValid()) continue;
            
            auto geomStream = element->GetGeometryStream();
            if (!geomStream.IsValid()) continue;
            
            // 分析几何复杂度
            auto complexity = AnalyzeGeometryComplexity(geomStream);
            analysis.totalTriangles += complexity.triangleCount;
            analysis.totalVertices += complexity.vertexCount;
            
            // 更新包围盒
            analysis.boundingBox.Extend(element->GetPlacement().GetElementBox());
            
            // 几何聚类
            ClusterGeometry(element, analysis.clusters);
        }
        
        analysis.geometryComplexity = CalculateOverallComplexity(analysis);
        return analysis;
    }
    
    // LOD 级别计算
    bvector<LODLevel> ComputeLODLevels(GeometryAnalysis const& analysis, 
                                      TileGenerationOptions const& options) {
        bvector<LODLevel> levels;
        
        // 基于几何复杂度和目标性能计算 LOD 级别
        double baseError = options.maxScreenSpaceError;
        double currentError = baseError;
        
        for (uint32_t level = 0; level < options.maxLODLevels; ++level) {
            LODLevel lodLevel;
            lodLevel.level = level;
            lodLevel.screenSpaceError = currentError;
            lodLevel.maxTrianglesPerTile = options.maxTrianglesPerTile >> level;
            lodLevel.geometryTolerance = options.baseTolerance * (1 << level);
            
            levels.push_back(lodLevel);
            currentError *= 2.0; // 每级误差翻倍
        }
        
        return levels;
    }
    
    // 空间分割
    struct SpatialPartition {
        DRange3d bounds;
        uint32_t lodLevel;
        bvector<DgnElementId> elements;
        bvector<std::unique_ptr<SpatialPartition>> children;
    };
    
    bvector<SpatialPartition> CreateSpatialPartition(DRange3d const& modelRange, 
                                                    bvector<LODLevel> const& lodLevels) {
        bvector<SpatialPartition> partitions;
        
        // 使用八叉树进行空间分割
        auto rootPartition = std::make_unique<SpatialPartition>();
        rootPartition->bounds = modelRange;
        rootPartition->lodLevel = 0;
        
        // 递归细分
        SubdividePartition(*rootPartition, lodLevels, 0);
        
        // 收集所有分区
        CollectPartitions(*rootPartition, partitions);
        
        return partitions;
    }
    
    void SubdividePartition(SpatialPartition& partition, 
                           bvector<LODLevel> const& lodLevels,
                           uint32_t currentLevel) {
        if (currentLevel >= lodLevels.size()) return;
        
        // 检查是否需要细分
        if (ShouldSubdividePartition(partition, lodLevels[currentLevel])) {
            // 八叉树细分
            auto subRanges = SubdivideRange(partition.bounds);
            
            for (auto& subRange : subRanges) {
                auto child = std::make_unique<SpatialPartition>();
                child->bounds = subRange;
                child->lodLevel = currentLevel + 1;
                
                // 递归细分
                SubdividePartition(*child, lodLevels, currentLevel + 1);
                
                partition.children.push_back(std::move(child));
            }
        }
    }
    
    // 瓦片内容生成
    BentleyStatus GenerateTileContent(DgnModelR model, 
                                     SpatialPartition const& partition,
                                     TileGenerationOptions const& options) {
        // 收集分区内的几何
        auto geometry = CollectGeometryInPartition(model, partition);
        
        // 几何简化和优化
        auto optimizedGeometry = OptimizeGeometry(geometry, partition.lodLevel, options);
        
        // 生成瓦片内容
        auto tileContent = CreateTileContent(optimizedGeometry, options);
        
        // 压缩和序列化
        auto compressedContent = CompressTileContent(tileContent, options);
        
        // 存储瓦片
        return StoreTileContent(partition, compressedContent);
    }
};
```

### 3. LOD (Level of Detail) 管理

```cpp
//! LOD 配置和管理
struct LevelOfDetailConfig {
    // LOD 级别定义
    struct LODLevel {
        uint32_t level;                    // LOD 级别 (0 = 最高细节)
        double screenSpaceError;           // 屏幕空间误差阈值
        double geometryTolerance;          // 几何简化容差
        uint32_t maxTrianglesPerTile;      // 每个瓦片最大三角形数
        uint32_t maxVerticesPerTile;       // 每个瓦片最大顶点数
        bool enableInstancing;             // 是否启用实例化
        bool enableOcclusion;              // 是否启用遮挡剔除
    };
    
    bvector<LODLevel> m_levels;
    double m_baseTolerance = 1e-6;
    uint32_t m_maxLevels = 16;
    
public:
    // 根据视图上下文选择合适的 LOD
    uint32_t SelectLOD(ViewContextR context, DRange3d const& tileBounds) const {
        // 计算瓦片的屏幕空间大小
        double screenSize = ComputeScreenSpaceSize(context, tileBounds);
        
        // 选择合适的 LOD 级别
        for (uint32_t i = 0; i < m_levels.size(); ++i) {
            if (screenSize >= m_levels[i].screenSpaceError) {
                return i;
            }
        }
        
        return m_levels.size() - 1; // 返回最低细节级别
    }
    
    // 计算屏幕空间大小
    double ComputeScreenSpaceSize(ViewContextR context, DRange3d const& bounds) const {
        // 获取视图参数
        auto viewMatrix = context.GetViewMatrix();
        auto projMatrix = context.GetProjectionMatrix();
        auto viewport = context.GetViewport();
        
        // 计算包围盒在屏幕空间的投影大小
        auto corners = GetBoundingBoxCorners(bounds);
        double maxScreenSize = 0.0;
        
        for (auto& corner : corners) {
            auto screenPoint = TransformToScreen(corner, viewMatrix, projMatrix, viewport);
            // 计算与其他角点的最大距离
            for (auto& otherCorner : corners) {
                auto otherScreenPoint = TransformToScreen(otherCorner, viewMatrix, projMatrix, viewport);
                double distance = screenPoint.Distance(otherScreenPoint);
                maxScreenSize = std::max(maxScreenSize, distance);
            }
        }
        
        return maxScreenSize;
    }
    
    // 几何简化
    BentleyStatus SimplifyGeometry(GeometryStreamR geometry, uint32_t lodLevel) const {
        if (lodLevel >= m_levels.size()) return ERROR;
        
        auto& level = m_levels[lodLevel];
        
        // 应用几何简化
        GeometrySimplifier simplifier;
        simplifier.SetTolerance(level.geometryTolerance);
        simplifier.SetMaxTriangles(level.maxTrianglesPerTile);
        simplifier.SetMaxVertices(level.maxVerticesPerTile);
        
        return simplifier.Simplify(geometry);
    }
};
```

### 4. 瓦片缓存系统

```cpp
//! 瓦片缓存管理
class TileCache {
private:
    // 缓存项
    struct CacheEntry {
        TileId tileId;
        std::unique_ptr<TileContent> content;
        std::chrono::system_clock::time_point lastAccess;
        size_t memoryUsage;
        uint32_t accessCount;
    };
    
    // LRU 缓存
    std::unordered_map<TileId, std::unique_ptr<CacheEntry>> m_cache;
    std::list<TileId> m_accessOrder;
    
    // 缓存配置
    size_t m_maxMemoryUsage = 512 * 1024 * 1024; // 512MB
    size_t m_maxEntries = 10000;
    size_t m_currentMemoryUsage = 0;
    
    mutable std::mutex m_mutex;
    
public:
    // 获取瓦片内容
    TileContent* GetTileContent(TileId const& tileId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_cache.find(tileId);
        if (it != m_cache.end()) {
            // 更新访问时间和顺序
            it->second->lastAccess = std::chrono::system_clock::now();
            it->second->accessCount++;
            
            // 移动到访问列表前端
            m_accessOrder.remove(tileId);
            m_accessOrder.push_front(tileId);
            
            return it->second->content.get();
        }
        
        return nullptr;
    }
    
    // 添加瓦片内容到缓存
    void AddTileContent(TileId const& tileId, std::unique_ptr<TileContent> content) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        size_t contentSize = content->GetMemoryUsage();
        
        // 检查是否需要清理缓存
        while (m_currentMemoryUsage + contentSize > m_maxMemoryUsage || 
               m_cache.size() >= m_maxEntries) {
            EvictLeastRecentlyUsed();
        }
        
        // 添加新条目
        auto entry = std::make_unique<CacheEntry>();
        entry->tileId = tileId;
        entry->content = std::move(content);
        entry->lastAccess = std::chrono::system_clock::now();
        entry->memoryUsage = contentSize;
        entry->accessCount = 1;
        
        m_cache[tileId] = std::move(entry);
        m_accessOrder.push_front(tileId);
        m_currentMemoryUsage += contentSize;
    }
    
    // 清理最近最少使用的条目
    void EvictLeastRecentlyUsed() {
        if (m_accessOrder.empty()) return;
        
        auto lruTileId = m_accessOrder.back();
        m_accessOrder.pop_back();
        
        auto it = m_cache.find(lruTileId);
        if (it != m_cache.end()) {
            m_currentMemoryUsage -= it->second->memoryUsage;
            m_cache.erase(it);
        }
    }
    
    // 预加载瓦片
    void PreloadTiles(bvector<TileId> const& tileIds, TileGenerator& generator) {
        // 异步预加载
        for (auto& tileId : tileIds) {
            if (m_cache.find(tileId) == m_cache.end()) {
                // 启动异步加载任务
                std::async(std::launch::async, [this, tileId, &generator]() {
                    auto content = generator.GenerateTileContent(tileId);
                    if (content) {
                        AddTileContent(tileId, std::move(content));
                    }
                });
            }
        }
    }
    
    // 缓存统计
    struct CacheStatistics {
        size_t totalEntries;
        size_t memoryUsage;
        double hitRate;
        double averageAccessCount;
    };
    
    CacheStatistics GetStatistics() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        CacheStatistics stats;
        stats.totalEntries = m_cache.size();
        stats.memoryUsage = m_currentMemoryUsage;
        
        size_t totalAccess = 0;
        for (auto& pair : m_cache) {
            totalAccess += pair.second->accessCount;
        }
        
        stats.averageAccessCount = stats.totalEntries > 0 ? 
            (double)totalAccess / stats.totalEntries : 0.0;
        
        return stats;
    }
};
```

### 5. 瓦片内容格式和序列化

```cpp
//! 瓦片内容定义
struct TileContent {
    // 内容类型
    enum class ContentType {
        Geometry,           // 几何内容
        PointCloud,         // 点云数据
        Instanced,          // 实例化几何
        Composite,          // 复合内容
        Empty               // 空瓦片
    };

    ContentType m_type;

    // 几何数据
    struct GeometryData {
        // 顶点数据
        bvector<DPoint3d> vertices;
        bvector<DVec3d> normals;
        bvector<DPoint2d> uvCoordinates;
        bvector<ColorDef> colors;

        // 索引数据
        bvector<uint32_t> indices;

        // 材质信息
        bvector<MaterialInfo> materials;

        // 实例化数据
        bvector<Transform> instances;

        // 包围盒
        DRange3d boundingBox;

        // 压缩信息
        CompressionInfo compression;
    };

    std::unique_ptr<GeometryData> m_geometryData;

    // 元数据
    struct Metadata {
        TileId tileId;
        uint32_t lodLevel;
        DRange3d bounds;
        double screenSpaceError;
        size_t originalSize;
        size_t compressedSize;
        std::chrono::system_clock::time_point creationTime;
    };

    Metadata m_metadata;

public:
    // 序列化到二进制格式
    BentleyStatus SerializeToBinary(BinaryStreamR stream) const {
        // 写入头部信息
        stream.Write(&m_metadata, sizeof(Metadata));
        stream.Write(&m_type, sizeof(ContentType));

        if (m_geometryData) {
            // 写入几何数据
            WriteGeometryData(stream, *m_geometryData);
        }

        return SUCCESS;
    }

    // 从二进制格式反序列化
    static std::unique_ptr<TileContent> DeserializeFromBinary(BinaryStreamR stream) {
        auto content = std::make_unique<TileContent>();

        // 读取头部信息
        stream.Read(&content->m_metadata, sizeof(Metadata));
        stream.Read(&content->m_type, sizeof(ContentType));

        if (content->m_type == ContentType::Geometry) {
            content->m_geometryData = std::make_unique<GeometryData>();
            ReadGeometryData(stream, *content->m_geometryData);
        }

        return content;
    }

    // 获取内存使用量
    size_t GetMemoryUsage() const {
        size_t usage = sizeof(TileContent);

        if (m_geometryData) {
            usage += m_geometryData->vertices.size() * sizeof(DPoint3d);
            usage += m_geometryData->normals.size() * sizeof(DVec3d);
            usage += m_geometryData->uvCoordinates.size() * sizeof(DPoint2d);
            usage += m_geometryData->colors.size() * sizeof(ColorDef);
            usage += m_geometryData->indices.size() * sizeof(uint32_t);
            usage += m_geometryData->materials.size() * sizeof(MaterialInfo);
            usage += m_geometryData->instances.size() * sizeof(Transform);
        }

        return usage;
    }

private:
    static void WriteGeometryData(BinaryStreamR stream, GeometryData const& data) {
        // 写入顶点数据
        uint32_t vertexCount = static_cast<uint32_t>(data.vertices.size());
        stream.Write(&vertexCount, sizeof(uint32_t));
        if (vertexCount > 0) {
            stream.Write(data.vertices.data(), vertexCount * sizeof(DPoint3d));
        }

        // 写入法线数据
        uint32_t normalCount = static_cast<uint32_t>(data.normals.size());
        stream.Write(&normalCount, sizeof(uint32_t));
        if (normalCount > 0) {
            stream.Write(data.normals.data(), normalCount * sizeof(DVec3d));
        }

        // 写入 UV 坐标
        uint32_t uvCount = static_cast<uint32_t>(data.uvCoordinates.size());
        stream.Write(&uvCount, sizeof(uint32_t));
        if (uvCount > 0) {
            stream.Write(data.uvCoordinates.data(), uvCount * sizeof(DPoint2d));
        }

        // 写入索引数据
        uint32_t indexCount = static_cast<uint32_t>(data.indices.size());
        stream.Write(&indexCount, sizeof(uint32_t));
        if (indexCount > 0) {
            stream.Write(data.indices.data(), indexCount * sizeof(uint32_t));
        }

        // 写入包围盒
        stream.Write(&data.boundingBox, sizeof(DRange3d));
    }

    static void ReadGeometryData(BinaryStreamR stream, GeometryData& data) {
        // 读取顶点数据
        uint32_t vertexCount;
        stream.Read(&vertexCount, sizeof(uint32_t));
        if (vertexCount > 0) {
            data.vertices.resize(vertexCount);
            stream.Read(data.vertices.data(), vertexCount * sizeof(DPoint3d));
        }

        // 读取法线数据
        uint32_t normalCount;
        stream.Read(&normalCount, sizeof(uint32_t));
        if (normalCount > 0) {
            data.normals.resize(normalCount);
            stream.Read(data.normals.data(), normalCount * sizeof(DVec3d));
        }

        // 读取 UV 坐标
        uint32_t uvCount;
        stream.Read(&uvCount, sizeof(uint32_t));
        if (uvCount > 0) {
            data.uvCoordinates.resize(uvCount);
            stream.Read(data.uvCoordinates.data(), uvCount * sizeof(DPoint2d));
        }

        // 读取索引数据
        uint32_t indexCount;
        stream.Read(&indexCount, sizeof(uint32_t));
        if (indexCount > 0) {
            data.indices.resize(indexCount);
            stream.Read(data.indices.data(), indexCount * sizeof(uint32_t));
        }

        // 读取包围盒
        stream.Read(&data.boundingBox, sizeof(DRange3d));
    }
};

//! 瓦片压缩器
class TileCompressor {
public:
    // 压缩选项
    struct CompressionOptions {
        bool enableGeometryCompression = true;
        bool enableTextureCompression = true;
        bool enableIndexCompression = true;
        double compressionQuality = 0.8;
        CompressionAlgorithm algorithm = CompressionAlgorithm::LZ4;
    };

    enum class CompressionAlgorithm {
        None,
        LZ4,
        ZSTD,
        Draco,      // 用于几何压缩
        Basis       // 用于纹理压缩
    };

    // 压缩瓦片内容
    std::unique_ptr<CompressedTileContent> Compress(TileContent const& content,
                                                   CompressionOptions const& options) {
        auto compressed = std::make_unique<CompressedTileContent>();
        compressed->originalSize = content.GetMemoryUsage();

        if (options.enableGeometryCompression && content.m_geometryData) {
            CompressGeometry(*content.m_geometryData, *compressed, options);
        }

        return compressed;
    }

private:
    void CompressGeometry(TileContent::GeometryData const& geometry,
                         CompressedTileContent& compressed,
                         CompressionOptions const& options) {
        switch (options.algorithm) {
            case CompressionAlgorithm::Draco:
                CompressWithDraco(geometry, compressed, options);
                break;
            case CompressionAlgorithm::LZ4:
                CompressWithLZ4(geometry, compressed, options);
                break;
            case CompressionAlgorithm::ZSTD:
                CompressWithZSTD(geometry, compressed, options);
                break;
            default:
                // 无压缩
                break;
        }
    }

    void CompressWithDraco(TileContent::GeometryData const& geometry,
                          CompressedTileContent& compressed,
                          CompressionOptions const& options) {
        // 使用 Draco 几何压缩库
        // 这里是伪代码，实际需要集成 Draco 库

        // 创建 Draco 编码器
        // draco::Encoder encoder;
        // encoder.SetAttributeQuantization(draco::GeometryAttribute::POSITION, 14);
        // encoder.SetAttributeQuantization(draco::GeometryAttribute::NORMAL, 10);
        // encoder.SetAttributeQuantization(draco::GeometryAttribute::TEX_COORD, 12);

        // 编码几何数据
        // auto dracoMesh = CreateDracoMesh(geometry);
        // draco::EncoderBuffer buffer;
        // encoder.EncodeMeshToBuffer(*dracoMesh, &buffer);

        // compressed.compressedData = std::vector<uint8_t>(buffer.data(), buffer.data() + buffer.size());
        // compressed.compressionRatio = (double)compressed.compressedData.size() / compressed.originalSize;
    }
};

//! 压缩后的瓦片内容
struct CompressedTileContent {
    bvector<uint8_t> compressedData;
    size_t originalSize;
    double compressionRatio;
    TileCompressor::CompressionAlgorithm algorithm;

    // 解压缩
    std::unique_ptr<TileContent> Decompress() const {
        // 根据压缩算法进行解压缩
        switch (algorithm) {
            case TileCompressor::CompressionAlgorithm::Draco:
                return DecompressWithDraco();
            case TileCompressor::CompressionAlgorithm::LZ4:
                return DecompressWithLZ4();
            case TileCompressor::CompressionAlgorithm::ZSTD:
                return DecompressWithZSTD();
            default:
                return nullptr;
        }
    }

private:
    std::unique_ptr<TileContent> DecompressWithDraco() const {
        // Draco 解压缩实现
        return nullptr;
    }

    std::unique_ptr<TileContent> DecompressWithLZ4() const {
        // LZ4 解压缩实现
        return nullptr;
    }

    std::unique_ptr<TileContent> DecompressWithZSTD() const {
        // ZSTD 解压缩实现
        return nullptr;
    }
};
```

### 6. 瓦片选择算法

```cpp
//! 瓦片选择器
class TileSelector {
private:
    // 视图上下文
    ViewContext* m_viewContext;

    // 选择配置
    struct SelectionConfig {
        double maxScreenSpaceError = 16.0;     // 最大屏幕空间误差（像素）
        uint32_t maxTilesPerFrame = 1000;      // 每帧最大瓦片数
        double frustumCullingMargin = 0.1;     // 视锥体剔除边距
        bool enableOcclusionCulling = true;    // 启用遮挡剔除
        bool enableTemporalCoherence = true;   // 启用时间一致性
        double lodBias = 1.0;                  // LOD 偏移
    };

    SelectionConfig m_config;

    // 上一帧的选择结果（用于时间一致性）
    TileSelection m_previousSelection;

public:
    // 主要选择算法
    TileSelection SelectTiles(TileTree& tileTree) {
        TileSelection selection;

        // 1. 视锥体剔除
        auto visibleTiles = PerformFrustumCulling(tileTree);

        // 2. 遮挡剔除
        if (m_config.enableOcclusionCulling) {
            visibleTiles = PerformOcclusionCulling(visibleTiles);
        }

        // 3. LOD 选择
        auto lodSelectedTiles = PerformLODSelection(visibleTiles);

        // 4. 时间一致性优化
        if (m_config.enableTemporalCoherence) {
            lodSelectedTiles = ApplyTemporalCoherence(lodSelectedTiles);
        }

        // 5. 限制瓦片数量
        selection.tiles = LimitTileCount(lodSelectedTiles);

        // 6. 计算加载优先级
        CalculateLoadPriorities(selection);

        m_previousSelection = selection;
        return selection;
    }

private:
    // 视锥体剔除
    bvector<Tile*> PerformFrustumCulling(TileTree& tileTree) {
        bvector<Tile*> visibleTiles;

        auto frustum = m_viewContext->GetViewFrustum();

        // 递归遍历瓦片树
        std::function<void(Tile*)> traverseTile = [&](Tile* tile) {
            if (!tile) return;

            // 扩展包围盒以包含边距
            auto expandedBounds = tile->m_range;
            expandedBounds.Extend(m_config.frustumCullingMargin);

            // 检查与视锥体的相交性
            auto intersection = frustum.IntersectsWith(expandedBounds);

            if (intersection == FrustumIntersection::Outside) {
                return; // 完全在视锥体外，跳过
            }

            if (intersection == FrustumIntersection::Inside ||
                intersection == FrustumIntersection::Intersecting) {

                // 检查是否需要细分
                if (tile->ShouldSubdivide(*m_viewContext)) {
                    // 遍历子瓦片
                    for (auto& child : tile->m_children) {
                        traverseTile(child.get());
                    }
                } else {
                    // 添加到可见瓦片列表
                    visibleTiles.push_back(tile);
                }
            }
        };

        traverseTile(tileTree.m_rootTile.get());
        return visibleTiles;
    }

    // 遮挡剔除
    bvector<Tile*> PerformOcclusionCulling(bvector<Tile*> const& visibleTiles) {
        bvector<Tile*> nonOccludedTiles;

        // 获取深度缓冲区
        auto depthBuffer = m_viewContext->GetDepthBuffer();
        if (!depthBuffer) {
            return visibleTiles; // 无深度缓冲区，跳过遮挡剔除
        }

        for (auto tile : visibleTiles) {
            // 将瓦片包围盒投影到屏幕空间
            auto screenBounds = ProjectToScreen(tile->m_range);

            // 检查是否被遮挡
            if (!IsOccluded(screenBounds, depthBuffer)) {
                nonOccludedTiles.push_back(tile);
            }
        }

        return nonOccludedTiles;
    }

    // LOD 选择
    bvector<Tile*> PerformLODSelection(bvector<Tile*> const& visibleTiles) {
        bvector<Tile*> selectedTiles;

        for (auto tile : visibleTiles) {
            // 计算屏幕空间误差
            double screenSpaceError = tile->ComputeScreenSpaceError(*m_viewContext);

            // 应用 LOD 偏移
            screenSpaceError *= m_config.lodBias;

            // 检查是否满足误差要求
            if (screenSpaceError <= m_config.maxScreenSpaceError) {
                selectedTiles.push_back(tile);
            } else {
                // 尝试使用更高细节的子瓦片
                auto childTiles = SelectChildTiles(tile);
                selectedTiles.insert(selectedTiles.end(), childTiles.begin(), childTiles.end());
            }
        }

        return selectedTiles;
    }

    // 时间一致性优化
    bvector<Tile*> ApplyTemporalCoherence(bvector<Tile*> const& currentSelection) {
        bvector<Tile*> optimizedSelection = currentSelection;

        // 保持上一帧中的瓦片（如果仍然可见）
        for (auto previousTile : m_previousSelection.tiles) {
            bool stillVisible = std::find(currentSelection.begin(), currentSelection.end(),
                                        previousTile) != currentSelection.end();

            if (stillVisible) {
                // 增加该瓦片的优先级
                // 这有助于减少瓦片的频繁切换
            }
        }

        return optimizedSelection;
    }

    // 限制瓦片数量
    bvector<Tile*> LimitTileCount(bvector<Tile*> const& tiles) {
        if (tiles.size() <= m_config.maxTilesPerFrame) {
            return tiles;
        }

        // 按优先级排序
        auto sortedTiles = tiles;
        std::sort(sortedTiles.begin(), sortedTiles.end(),
                 [this](Tile* a, Tile* b) {
                     return CalculateTilePriority(a) > CalculateTilePriority(b);
                 });

        // 返回前 N 个瓦片
        sortedTiles.resize(m_config.maxTilesPerFrame);
        return sortedTiles;
    }

    // 计算瓦片优先级
    double CalculateTilePriority(Tile* tile) {
        // 基于多个因素计算优先级：
        // 1. 屏幕空间大小
        // 2. 距离相机的距离
        // 3. 是否在视图中心
        // 4. 上一帧是否可见

        double screenSize = ComputeScreenSpaceSize(tile->m_range);
        double distance = ComputeDistanceToCamera(tile->m_range);
        double centerWeight = ComputeCenterWeight(tile->m_range);
        double temporalWeight = WasVisibleLastFrame(tile) ? 1.2 : 1.0;

        return (screenSize * centerWeight * temporalWeight) / (distance + 1.0);
    }
};
```

### 7. 渲染系统集成

```cpp
//! 瓦片渲染器
class TileRenderer {
private:
    // 渲染上下文
    RenderContext* m_renderContext;

    // GPU 资源管理
    std::unique_ptr<TileGpuResourceManager> m_gpuResourceManager;

    // 渲染统计
    struct RenderStatistics {
        uint32_t tilesRendered = 0;
        uint32_t trianglesRendered = 0;
        uint32_t drawCalls = 0;
        uint32_t gpuMemoryUsed = 0;
        std::chrono::milliseconds renderTime{0};
    };

    mutable RenderStatistics m_statistics;

public:
    // 渲染瓦片选择
    BentleyStatus RenderTileSelection(TileSelection const& selection) {
        m_statistics = RenderStatistics(); // 重置统计
        auto startTime = std::chrono::high_resolution_clock::now();

        // 1. 准备渲染状态
        PrepareRenderState();

        // 2. 按材质分组瓦片
        auto materialGroups = GroupTilesByMaterial(selection.tiles);

        // 3. 渲染每个材质组
        for (auto& group : materialGroups) {
            RenderMaterialGroup(group);
        }

        // 4. 清理渲染状态
        CleanupRenderState();

        auto endTime = std::chrono::high_resolution_clock::now();
        m_statistics.renderTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        return SUCCESS;
    }

private:
    // 材质分组
    struct MaterialGroup {
        MaterialInfo material;
        bvector<Tile*> tiles;
    };

    bvector<MaterialGroup> GroupTilesByMaterial(bvector<Tile*> const& tiles) {
        std::unordered_map<MaterialId, MaterialGroup> groups;

        for (auto tile : tiles) {
            if (!tile->m_content) continue;

            auto& geometryData = tile->m_content->m_geometryData;
            if (!geometryData) continue;

            for (auto& material : geometryData->materials) {
                groups[material.id].material = material;
                groups[material.id].tiles.push_back(tile);
            }
        }

        bvector<MaterialGroup> result;
        for (auto& pair : groups) {
            result.push_back(std::move(pair.second));
        }

        return result;
    }

    // 渲染材质组
    void RenderMaterialGroup(MaterialGroup const& group) {
        // 设置材质状态
        SetMaterialState(group.material);

        // 渲染该材质的所有瓦片
        for (auto tile : group.tiles) {
            RenderTile(tile);
        }

        m_statistics.drawCalls++;
    }

    // 渲染单个瓦片
    void RenderTile(Tile* tile) {
        if (!tile->m_content || !tile->m_content->m_geometryData) {
            return;
        }

        auto& geometry = *tile->m_content->m_geometryData;

        // 获取或创建 GPU 资源
        auto gpuResource = m_gpuResourceManager->GetOrCreateResource(tile->m_id, geometry);
        if (!gpuResource) {
            return;
        }

        // 设置变换矩阵
        SetTransformMatrix(tile);

        // 绑定顶点缓冲区
        BindVertexBuffers(gpuResource);

        // 绑定索引缓冲区
        BindIndexBuffer(gpuResource);

        // 执行绘制调用
        if (geometry.instances.empty()) {
            // 普通绘制
            DrawIndexed(geometry.indices.size());
        } else {
            // 实例化绘制
            DrawIndexedInstanced(geometry.indices.size(), geometry.instances.size());
        }

        m_statistics.tilesRendered++;
        m_statistics.trianglesRendered += geometry.indices.size() / 3;
    }

    void PrepareRenderState() {
        // 设置渲染状态
        m_renderContext->SetDepthTest(true);
        m_renderContext->SetCulling(true);
        m_renderContext->SetBlending(false);
    }

    void CleanupRenderState() {
        // 恢复默认渲染状态
        m_renderContext->RestoreDefaultState();
    }

    void SetMaterialState(MaterialInfo const& material) {
        // 设置着色器
        m_renderContext->SetShader(material.shaderId);

        // 设置纹理
        for (size_t i = 0; i < material.textures.size(); ++i) {
            m_renderContext->SetTexture(i, material.textures[i]);
        }

        // 设置材质参数
        m_renderContext->SetMaterialParameters(material.parameters);
    }

    void SetTransformMatrix(Tile* tile) {
        // 计算世界变换矩阵
        auto worldMatrix = ComputeWorldMatrix(tile);
        m_renderContext->SetWorldMatrix(worldMatrix);
    }
};

//! GPU 资源管理器
class TileGpuResourceManager {
private:
    // GPU 资源
    struct GpuResource {
        uint32_t vertexBufferId = 0;
        uint32_t indexBufferId = 0;
        uint32_t vertexArrayId = 0;
        size_t memoryUsage = 0;
        std::chrono::system_clock::time_point lastUsed;
    };

    std::unordered_map<TileId, std::unique_ptr<GpuResource>> m_resources;
    size_t m_totalGpuMemory = 0;
    size_t m_maxGpuMemory = 1024 * 1024 * 1024; // 1GB

    mutable std::mutex m_mutex;

public:
    // 获取或创建 GPU 资源
    GpuResource* GetOrCreateResource(TileId const& tileId,
                                   TileContent::GeometryData const& geometry) {
        std::lock_guard<std::mutex> lock(m_mutex);

        auto it = m_resources.find(tileId);
        if (it != m_resources.end()) {
            it->second->lastUsed = std::chrono::system_clock::now();
            return it->second.get();
        }

        // 检查 GPU 内存限制
        size_t requiredMemory = EstimateGpuMemoryUsage(geometry);
        while (m_totalGpuMemory + requiredMemory > m_maxGpuMemory) {
            EvictLeastRecentlyUsedResource();
        }

        // 创建新的 GPU 资源
        auto resource = CreateGpuResource(geometry);
        if (resource) {
            resource->memoryUsage = requiredMemory;
            resource->lastUsed = std::chrono::system_clock::now();
            m_totalGpuMemory += requiredMemory;

            auto result = resource.get();
            m_resources[tileId] = std::move(resource);
            return result;
        }

        return nullptr;
    }

    // 清理未使用的资源
    void CleanupUnusedResources(std::chrono::seconds maxAge = std::chrono::seconds(60)) {
        std::lock_guard<std::mutex> lock(m_mutex);

        auto now = std::chrono::system_clock::now();
        auto it = m_resources.begin();

        while (it != m_resources.end()) {
            auto age = std::chrono::duration_cast<std::chrono::seconds>(now - it->second->lastUsed);

            if (age > maxAge) {
                DestroyGpuResource(*it->second);
                m_totalGpuMemory -= it->second->memoryUsage;
                it = m_resources.erase(it);
            } else {
                ++it;
            }
        }
    }

private:
    std::unique_ptr<GpuResource> CreateGpuResource(TileContent::GeometryData const& geometry) {
        auto resource = std::make_unique<GpuResource>();

        // 创建顶点缓冲区
        glGenBuffers(1, &resource->vertexBufferId);
        glBindBuffer(GL_ARRAY_BUFFER, resource->vertexBufferId);

        // 计算顶点数据大小
        size_t vertexDataSize = geometry.vertices.size() * sizeof(DPoint3d) +
                               geometry.normals.size() * sizeof(DVec3d) +
                               geometry.uvCoordinates.size() * sizeof(DPoint2d) +
                               geometry.colors.size() * sizeof(ColorDef);

        // 上传顶点数据
        glBufferData(GL_ARRAY_BUFFER, vertexDataSize, nullptr, GL_STATIC_DRAW);

        size_t offset = 0;
        if (!geometry.vertices.empty()) {
            glBufferSubData(GL_ARRAY_BUFFER, offset,
                           geometry.vertices.size() * sizeof(DPoint3d),
                           geometry.vertices.data());
            offset += geometry.vertices.size() * sizeof(DPoint3d);
        }

        if (!geometry.normals.empty()) {
            glBufferSubData(GL_ARRAY_BUFFER, offset,
                           geometry.normals.size() * sizeof(DVec3d),
                           geometry.normals.data());
            offset += geometry.normals.size() * sizeof(DVec3d);
        }

        // 创建索引缓冲区
        if (!geometry.indices.empty()) {
            glGenBuffers(1, &resource->indexBufferId);
            glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, resource->indexBufferId);
            glBufferData(GL_ELEMENT_ARRAY_BUFFER,
                        geometry.indices.size() * sizeof(uint32_t),
                        geometry.indices.data(), GL_STATIC_DRAW);
        }

        // 创建顶点数组对象
        glGenVertexArrays(1, &resource->vertexArrayId);
        glBindVertexArray(resource->vertexArrayId);

        // 设置顶点属性
        SetupVertexAttributes(geometry);

        // 解绑
        glBindVertexArray(0);
        glBindBuffer(GL_ARRAY_BUFFER, 0);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);

        return resource;
    }

    void DestroyGpuResource(GpuResource const& resource) {
        if (resource.vertexBufferId != 0) {
            glDeleteBuffers(1, &resource.vertexBufferId);
        }
        if (resource.indexBufferId != 0) {
            glDeleteBuffers(1, &resource.indexBufferId);
        }
        if (resource.vertexArrayId != 0) {
            glDeleteVertexArrays(1, &resource.vertexArrayId);
        }
    }

    size_t EstimateGpuMemoryUsage(TileContent::GeometryData const& geometry) {
        size_t usage = 0;
        usage += geometry.vertices.size() * sizeof(DPoint3d);
        usage += geometry.normals.size() * sizeof(DVec3d);
        usage += geometry.uvCoordinates.size() * sizeof(DPoint2d);
        usage += geometry.colors.size() * sizeof(ColorDef);
        usage += geometry.indices.size() * sizeof(uint32_t);
        return usage;
    }

    void EvictLeastRecentlyUsedResource() {
        if (m_resources.empty()) return;

        auto oldestIt = m_resources.begin();
        auto oldestTime = oldestIt->second->lastUsed;

        for (auto it = m_resources.begin(); it != m_resources.end(); ++it) {
            if (it->second->lastUsed < oldestTime) {
                oldestTime = it->second->lastUsed;
                oldestIt = it;
            }
        }

        DestroyGpuResource(*oldestIt->second);
        m_totalGpuMemory -= oldestIt->second->memoryUsage;
        m_resources.erase(oldestIt);
    }
};
```

### 8. 异步加载系统

```cpp
//! 异步瓦片加载器
class AsyncTileLoader {
private:
    // 加载任务
    struct LoadTask {
        TileId tileId;
        uint32_t priority;
        std::chrono::system_clock::time_point submitTime;
        std::promise<std::unique_ptr<TileContent>> promise;

        LoadTask(TileId id, uint32_t prio)
            : tileId(id), priority(prio), submitTime(std::chrono::system_clock::now()) {}
    };

    // 任务队列（优先级队列）
    struct TaskComparator {
        bool operator()(std::shared_ptr<LoadTask> const& a,
                       std::shared_ptr<LoadTask> const& b) const {
            return a->priority < b->priority; // 高优先级在前
        }
    };

    std::priority_queue<std::shared_ptr<LoadTask>,
                       std::vector<std::shared_ptr<LoadTask>>,
                       TaskComparator> m_taskQueue;

    // 工作线程
    std::vector<std::thread> m_workers;
    std::atomic<bool> m_shutdown{false};
    std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;

    // 瓦片生成器
    std::unique_ptr<TileGenerator> m_generator;

    // 加载统计
    struct LoadStatistics {
        std::atomic<uint32_t> tasksSubmitted{0};
        std::atomic<uint32_t> tasksCompleted{0};
        std::atomic<uint32_t> tasksFailed{0};
        std::atomic<uint32_t> tasksInProgress{0};
        std::atomic<double> averageLoadTime{0.0};
    };

    mutable LoadStatistics m_statistics;

public:
    AsyncTileLoader(size_t numWorkers = 4) {
        m_generator = std::make_unique<TileGenerator>();

        // 启动工作线程
        for (size_t i = 0; i < numWorkers; ++i) {
            m_workers.emplace_back([this]() { WorkerThread(); });
        }
    }

    ~AsyncTileLoader() {
        Shutdown();
    }

    // 提交加载任务
    std::future<std::unique_ptr<TileContent>> LoadTileAsync(TileId const& tileId,
                                                           uint32_t priority = 0) {
        auto task = std::make_shared<LoadTask>(tileId, priority);
        auto future = task->promise.get_future();

        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            m_taskQueue.push(task);
            m_statistics.tasksSubmitted++;
        }

        m_queueCondition.notify_one();
        return future;
    }

    // 批量加载
    std::vector<std::future<std::unique_ptr<TileContent>>> LoadTilesBatch(
        std::vector<std::pair<TileId, uint32_t>> const& requests) {

        std::vector<std::future<std::unique_ptr<TileContent>>> futures;

        {
            std::lock_guard<std::mutex> lock(m_queueMutex);

            for (auto& request : requests) {
                auto task = std::make_shared<LoadTask>(request.first, request.second);
                futures.push_back(task->promise.get_future());
                m_taskQueue.push(task);
                m_statistics.tasksSubmitted++;
            }
        }

        m_queueCondition.notify_all();
        return futures;
    }

    // 取消低优先级任务
    void CancelLowPriorityTasks(uint32_t minPriority) {
        std::lock_guard<std::mutex> lock(m_queueMutex);

        // 重建队列，排除低优先级任务
        std::priority_queue<std::shared_ptr<LoadTask>,
                           std::vector<std::shared_ptr<LoadTask>>,
                           TaskComparator> newQueue;

        while (!m_taskQueue.empty()) {
            auto task = m_taskQueue.top();
            m_taskQueue.pop();

            if (task->priority >= minPriority) {
                newQueue.push(task);
            } else {
                // 取消任务
                task->promise.set_exception(
                    std::make_exception_ptr(std::runtime_error("Task cancelled")));
            }
        }

        m_taskQueue = std::move(newQueue);
    }

    // 获取加载统计
    LoadStatistics GetStatistics() const {
        return m_statistics;
    }

    void Shutdown() {
        m_shutdown.store(true);
        m_queueCondition.notify_all();

        for (auto& worker : m_workers) {
            if (worker.joinable()) {
                worker.join();
            }
        }
    }

private:
    void WorkerThread() {
        while (!m_shutdown.load()) {
            std::shared_ptr<LoadTask> task;

            // 获取任务
            {
                std::unique_lock<std::mutex> lock(m_queueMutex);
                m_queueCondition.wait(lock, [this]() {
                    return !m_taskQueue.empty() || m_shutdown.load();
                });

                if (m_shutdown.load()) break;

                if (!m_taskQueue.empty()) {
                    task = m_taskQueue.top();
                    m_taskQueue.pop();
                    m_statistics.tasksInProgress++;
                }
            }

            if (task) {
                // 执行加载任务
                auto startTime = std::chrono::high_resolution_clock::now();

                try {
                    auto content = m_generator->GenerateTileContent(task->tileId);
                    task->promise.set_value(std::move(content));
                    m_statistics.tasksCompleted++;
                } catch (...) {
                    task->promise.set_exception(std::current_exception());
                    m_statistics.tasksFailed++;
                }

                auto endTime = std::chrono::high_resolution_clock::now();
                auto loadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

                // 更新平均加载时间
                double currentAvg = m_statistics.averageLoadTime.load();
                double newAvg = (currentAvg * (m_statistics.tasksCompleted - 1) + loadTime.count()) /
                               m_statistics.tasksCompleted;
                m_statistics.averageLoadTime.store(newAvg);

                m_statistics.tasksInProgress--;
            }
        }
    }
};
```

### 9. 性能优化策略

```cpp
//! 瓦片性能优化器
class TilePerformanceOptimizer {
public:
    // 优化配置
    struct OptimizationConfig {
        bool enableGeometryInstancing = true;      // 几何实例化
        bool enableTexturePacking = true;         // 纹理打包
        bool enableMeshOptimization = true;       // 网格优化
        bool enableOcclusionCulling = true;       // 遮挡剔除
        bool enableFrustumCulling = true;         // 视锥体剔除
        bool enableLODOptimization = true;        // LOD 优化
        bool enableMemoryPooling = true;          // 内存池化
        bool enableAsyncLoading = true;           // 异步加载

        // 性能目标
        uint32_t targetFPS = 60;                  // 目标帧率
        uint32_t maxTilesPerFrame = 1000;         // 每帧最大瓦片数
        uint32_t maxTrianglesPerFrame = 1000000;  // 每帧最大三角形数
        size_t maxGpuMemoryMB = 1024;             // 最大 GPU 内存
        size_t maxSystemMemoryMB = 2048;          // 最大系统内存
    };

private:
    OptimizationConfig m_config;

    // 性能监控
    struct PerformanceMetrics {
        double currentFPS = 0.0;
        uint32_t currentTileCount = 0;
        uint32_t currentTriangleCount = 0;
        size_t currentGpuMemoryMB = 0;
        size_t currentSystemMemoryMB = 0;

        // 历史数据
        std::deque<double> fpsHistory;
        std::deque<uint32_t> tileCountHistory;

        void UpdateMetrics(double fps, uint32_t tiles, uint32_t triangles) {
            currentFPS = fps;
            currentTileCount = tiles;
            currentTriangleCount = triangles;

            fpsHistory.push_back(fps);
            tileCountHistory.push_back(tiles);

            // 保持历史数据在合理范围内
            if (fpsHistory.size() > 60) fpsHistory.pop_front();
            if (tileCountHistory.size() > 60) tileCountHistory.pop_front();
        }

        double GetAverageFPS() const {
            if (fpsHistory.empty()) return 0.0;
            return std::accumulate(fpsHistory.begin(), fpsHistory.end(), 0.0) / fpsHistory.size();
        }
    };

    mutable PerformanceMetrics m_metrics;

public:
    // 动态优化
    void OptimizePerformance(TileTreeManager& tileManager, ViewContext& viewContext) {
        // 更新性能指标
        UpdatePerformanceMetrics(viewContext);

        // 根据性能指标调整优化策略
        if (m_metrics.currentFPS < m_config.targetFPS * 0.8) {
            // 性能不足，启用更激进的优化
            ApplyAggressiveOptimizations(tileManager, viewContext);
        } else if (m_metrics.currentFPS > m_config.targetFPS * 1.2) {
            // 性能过剩，可以提高质量
            RelaxOptimizations(tileManager, viewContext);
        }

        // 内存优化
        if (m_metrics.currentGpuMemoryMB > m_config.maxGpuMemoryMB * 0.9) {
            OptimizeGpuMemoryUsage(tileManager);
        }

        if (m_metrics.currentSystemMemoryMB > m_config.maxSystemMemoryMB * 0.9) {
            OptimizeSystemMemoryUsage(tileManager);
        }
    }

private:
    void UpdatePerformanceMetrics(ViewContext& viewContext) {
        // 获取当前帧率
        double fps = viewContext.GetCurrentFPS();

        // 获取当前渲染统计
        auto renderStats = viewContext.GetRenderStatistics();

        m_metrics.UpdateMetrics(fps, renderStats.tilesRendered, renderStats.trianglesRendered);

        // 获取内存使用情况
        m_metrics.currentGpuMemoryMB = GetGpuMemoryUsage() / (1024 * 1024);
        m_metrics.currentSystemMemoryMB = GetSystemMemoryUsage() / (1024 * 1024);
    }

    void ApplyAggressiveOptimizations(TileTreeManager& tileManager, ViewContext& viewContext) {
        // 1. 降低 LOD 质量
        auto lodConfig = tileManager.GetLODConfig();
        lodConfig.m_baseTolerance *= 1.5; // 增加容差
        for (auto& level : lodConfig.m_levels) {
            level.maxTrianglesPerTile = static_cast<uint32_t>(level.maxTrianglesPerTile * 0.8);
            level.screenSpaceError *= 1.2;
        }
        tileManager.SetLODConfig(lodConfig);

        // 2. 减少瓦片数量
        auto selector = tileManager.GetTileSelector();
        auto config = selector->GetConfig();
        config.maxTilesPerFrame = static_cast<uint32_t>(config.maxTilesPerFrame * 0.8);
        config.maxScreenSpaceError *= 1.3;
        selector->SetConfig(config);

        // 3. 启用更激进的剔除
        config.enableOcclusionCulling = true;
        config.frustumCullingMargin = 0.05; // 减少边距

        // 4. 降低纹理质量
        auto textureManager = tileManager.GetTextureManager();
        textureManager->SetMaxTextureSize(512); // 降低到 512x512
        textureManager->SetCompressionQuality(0.6);
    }

    void RelaxOptimizations(TileTreeManager& tileManager, ViewContext& viewContext) {
        // 1. 提高 LOD 质量
        auto lodConfig = tileManager.GetLODConfig();
        lodConfig.m_baseTolerance *= 0.8; // 减少容差
        for (auto& level : lodConfig.m_levels) {
            level.maxTrianglesPerTile = static_cast<uint32_t>(level.maxTrianglesPerTile * 1.2);
            level.screenSpaceError *= 0.9;
        }
        tileManager.SetLODConfig(lodConfig);

        // 2. 增加瓦片数量
        auto selector = tileManager.GetTileSelector();
        auto config = selector->GetConfig();
        config.maxTilesPerFrame = static_cast<uint32_t>(config.maxTilesPerFrame * 1.1);
        config.maxScreenSpaceError *= 0.9;
        selector->SetConfig(config);

        // 3. 提高纹理质量
        auto textureManager = tileManager.GetTextureManager();
        textureManager->SetMaxTextureSize(1024); // 提高到 1024x1024
        textureManager->SetCompressionQuality(0.8);
    }

    void OptimizeGpuMemoryUsage(TileTreeManager& tileManager) {
        // 1. 清理未使用的 GPU 资源
        auto gpuManager = tileManager.GetGpuResourceManager();
        gpuManager->CleanupUnusedResources(std::chrono::seconds(30));

        // 2. 降低纹理质量
        auto textureManager = tileManager.GetTextureManager();
        textureManager->CompressTextures();

        // 3. 减少瓦片缓存大小
        auto cache = tileManager.GetTileCache();
        cache->SetMaxMemoryUsage(m_config.maxGpuMemoryMB * 1024 * 1024 / 2);
    }

    void OptimizeSystemMemoryUsage(TileTreeManager& tileManager) {
        // 1. 清理瓦片缓存
        auto cache = tileManager.GetTileCache();
        cache->EvictLeastRecentlyUsed();

        // 2. 减少预加载
        auto loader = tileManager.GetAsyncLoader();
        loader->CancelLowPriorityTasks(5); // 取消优先级低于 5 的任务

        // 3. 强制垃圾回收
        tileManager.ForceGarbageCollection();
    }
};

//! 瓦片质量评估器
class TileQualityAssessor {
public:
    // 质量指标
    struct QualityMetrics {
        double geometricAccuracy = 0.0;        // 几何精度
        double visualFidelity = 0.0;           // 视觉保真度
        double compressionEfficiency = 0.0;    // 压缩效率
        double renderingPerformance = 0.0;     // 渲染性能
        double memoryEfficiency = 0.0;         // 内存效率

        double overallQuality = 0.0;           // 总体质量评分
    };

    // 评估瓦片质量
    QualityMetrics AssessTileQuality(Tile const& tile, TileContent const& content) {
        QualityMetrics metrics;

        // 1. 几何精度评估
        metrics.geometricAccuracy = AssessGeometricAccuracy(tile, content);

        // 2. 视觉保真度评估
        metrics.visualFidelity = AssessVisualFidelity(content);

        // 3. 压缩效率评估
        metrics.compressionEfficiency = AssessCompressionEfficiency(content);

        // 4. 渲染性能评估
        metrics.renderingPerformance = AssessRenderingPerformance(content);

        // 5. 内存效率评估
        metrics.memoryEfficiency = AssessMemoryEfficiency(content);

        // 计算总体质量评分
        metrics.overallQuality = CalculateOverallQuality(metrics);

        return metrics;
    }

private:
    double AssessGeometricAccuracy(Tile const& tile, TileContent const& content) {
        if (!content.m_geometryData) return 0.0;

        auto& geometry = *content.m_geometryData;

        // 基于三角形密度和包围盒大小评估精度
        double volume = tile.m_range.Volume();
        double triangleDensity = geometry.indices.size() / 3.0 / volume;

        // 归一化到 0-1 范围
        return std::min(1.0, triangleDensity / 1000.0);
    }

    double AssessVisualFidelity(TileContent const& content) {
        if (!content.m_geometryData) return 0.0;

        auto& geometry = *content.m_geometryData;

        double score = 0.0;

        // 法线质量
        if (!geometry.normals.empty()) {
            score += 0.3;
        }

        // UV 坐标质量
        if (!geometry.uvCoordinates.empty()) {
            score += 0.2;
        }

        // 颜色信息
        if (!geometry.colors.empty()) {
            score += 0.2;
        }

        // 材质信息
        if (!geometry.materials.empty()) {
            score += 0.3;
        }

        return score;
    }

    double AssessCompressionEfficiency(TileContent const& content) {
        if (content.m_metadata.originalSize == 0) return 0.0;

        double compressionRatio = (double)content.m_metadata.compressedSize /
                                 content.m_metadata.originalSize;

        // 压缩比越小越好，但要平衡质量损失
        return 1.0 - compressionRatio;
    }

    double AssessRenderingPerformance(TileContent const& content) {
        if (!content.m_geometryData) return 1.0;

        auto& geometry = *content.m_geometryData;

        // 基于三角形数量评估渲染性能
        uint32_t triangleCount = geometry.indices.size() / 3;

        // 理想的三角形数量范围
        if (triangleCount < 1000) {
            return 1.0; // 很好
        } else if (triangleCount < 10000) {
            return 0.8; // 良好
        } else if (triangleCount < 50000) {
            return 0.6; // 一般
        } else {
            return 0.4; // 较差
        }
    }

    double AssessMemoryEfficiency(TileContent const& content) {
        size_t memoryUsage = content.GetMemoryUsage();

        // 基于内存使用量评估效率
        if (memoryUsage < 1024 * 1024) {        // < 1MB
            return 1.0;
        } else if (memoryUsage < 10 * 1024 * 1024) {  // < 10MB
            return 0.8;
        } else if (memoryUsage < 50 * 1024 * 1024) {  // < 50MB
            return 0.6;
        } else {
            return 0.4;
        }
    }

    double CalculateOverallQuality(QualityMetrics const& metrics) {
        // 加权平均
        double weights[] = {0.25, 0.25, 0.15, 0.20, 0.15};
        double values[] = {
            metrics.geometricAccuracy,
            metrics.visualFidelity,
            metrics.compressionEfficiency,
            metrics.renderingPerformance,
            metrics.memoryEfficiency
        };

        double weightedSum = 0.0;
        for (int i = 0; i < 5; ++i) {
            weightedSum += weights[i] * values[i];
        }

        return weightedSum;
    }
};
```

## 总结

### 🎯 iModel Native Tile 生成架构特点

1. **分层架构设计**
   - **TileTreeManager**: 统一管理瓦片树和生成流程
   - **TileGenerator**: 负责瓦片内容的生成和优化
   - **TileCache**: 高效的 LRU 缓存系统
   - **AsyncTileLoader**: 异步加载和优先级管理

2. **高性能 LOD 管理**
   - 基于屏幕空间误差的动态 LOD 选择
   - 几何简化和优化算法
   - 自适应质量调整机制

3. **智能瓦片选择**
   - 视锥体剔除和遮挡剔除
   - 时间一致性优化
   - 优先级驱动的加载策略

4. **渲染系统集成**
   - GPU 资源管理和优化
   - 材质分组和批量渲染
   - 实例化渲染支持

5. **内存和性能优化**
   - 多级缓存系统
   - 动态性能调整
   - 内存池化管理

### 📊 性能指标

| 指标 | 目标值 | 优化策略 |
|------|--------|----------|
| **帧率** | 60 FPS | 动态 LOD 调整、剔除优化 |
| **瓦片数量** | < 1000/帧 | 智能选择算法 |
| **内存使用** | < 2GB | LRU 缓存、压缩 |
| **加载延迟** | < 100ms | 异步加载、预测 |

### 🚀 技术优势

1. **可扩展性**: 支持大规模场景和复杂几何
2. **自适应性**: 根据硬件性能动态调整
3. **高效性**: 多级优化和缓存机制
4. **稳定性**: 完整的错误处理和恢复机制

这个 Tile 生成架构为 iModel 的高性能 3D 渲染提供了坚实的基础，能够处理从简单模型到复杂工程场景的各种需求。
```
```
```
