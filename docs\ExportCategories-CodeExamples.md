# ExportCategories 代码示例文档

## 基础用法示例

### 1. 简单的类别排除

```typescript
import { IModelExporter } from "@itwin/transformer";
import { SnapshotDb } from "@itwin/core-backend";

// 创建导出器
const sourceDb = SnapshotDb.openFile("source.bim");
const exporter = new IModelExporter(sourceDb);

// 排除特定类别
const unwantedCategoryId = "0x123456789abcdef0";
exporter.excludeElementsInCategory(unwantedCategoryId);

// 执行导出
await exporter.exportAll();
```

### 2. 批量类别排除

```typescript
// 排除多个类别
const categoriesToExclude = [
  "0x123456789abcdef0",
  "0x123456789abcdef1", 
  "0x123456789abcdef2"
];

categoriesToExclude.forEach(categoryId => {
  exporter.excludeElementsInCategory(categoryId);
});
```

### 3. 基于类别名称的排除

```typescript
import { SpatialCategory, IModel } from "@itwin/core-backend";

// 通过类别名称查找并排除
const categoryNames = ["Temporary", "Construction", "Hidden"];

for (const categoryName of categoryNames) {
  const categoryCode = SpatialCategory.createCode(
    sourceDb,
    IModel.dictionaryId,
    categoryName
  );
  
  try {
    const categoryId = sourceDb.elements.queryElementIdByCode(categoryCode);
    if (categoryId) {
      exporter.excludeElementsInCategory(categoryId);
      console.log(`Excluded category: ${categoryName}`);
    }
  } catch (error) {
    console.warn(`Category not found: ${categoryName}`);
  }
}
```

## 高级用法示例

### 4. 自定义导出处理器

```typescript
import { IModelExportHandler, IModelExporter } from "@itwin/transformer";
import { Element, GeometricElement } from "@itwin/core-backend";

class CustomCategoryExportHandler extends IModelExportHandler {
  private allowedCategories: Set<string>;
  
  constructor(allowedCategories: string[]) {
    super();
    this.allowedCategories = new Set(allowedCategories);
  }
  
  public override shouldExportElement(element: Element): boolean {
    // 只导出指定类别的几何元素
    if (element instanceof GeometricElement) {
      return this.allowedCategories.has(element.category);
    }
    
    // 非几何元素默认导出
    return true;
  }
  
  public override onExportElement(element: Element): void {
    console.log(`Exporting element: ${element.id} in category: ${
      element instanceof GeometricElement ? element.category : 'N/A'
    }`);
  }
}

// 使用自定义处理器
const allowedCategories = ["0x123", "0x456", "0x789"];
const handler = new CustomCategoryExportHandler(allowedCategories);
exporter.registerHandler(handler);
```

### 5. 基于视图的类别过滤

```typescript
import { 
  CategorySelector, 
  ViewDefinition, 
  SpatialViewDefinition 
} from "@itwin/core-backend";
import { Id64 } from "@itwin/core-bentley";

class ViewBasedCategoryFilter {
  private exporter: IModelExporter;
  private sourceDb: SnapshotDb;
  
  constructor(exporter: IModelExporter, sourceDb: SnapshotDb) {
    this.exporter = exporter;
    this.sourceDb = sourceDb;
  }
  
  public filterByView(viewDefinitionId: string): void {
    // 获取视图定义
    const viewDef = this.sourceDb.elements.getElement<SpatialViewDefinition>(
      viewDefinitionId,
      SpatialViewDefinition
    );
    
    // 获取类别选择器
    const categorySelector = this.sourceDb.elements.getElement<CategorySelector>(
      viewDef.categorySelectorId,
      CategorySelector
    );
    
    // 获取允许的类别集合
    const allowedCategories = new Set(categorySelector.categories);
    
    // 查询所有空间类别并排除不在视图中的
    const sql = `SELECT ECInstanceId FROM BisCore:SpatialCategory`;
    this.sourceDb.withPreparedStatement(sql, (statement) => {
      while (statement.step() === DbResult.BE_SQLITE_ROW) {
        const categoryId = statement.getValue(0).getId();
        if (!allowedCategories.has(categoryId)) {
          this.exporter.excludeElementsInCategory(categoryId);
        }
      }
    });
  }
}

// 使用示例
const filter = new ViewBasedCategoryFilter(exporter, sourceDb);
filter.filterByView("0x987654321");
```

### 6. 类别统计和分析

```typescript
class CategoryAnalyzer {
  private sourceDb: SnapshotDb;
  
  constructor(sourceDb: SnapshotDb) {
    this.sourceDb = sourceDb;
  }
  
  public analyzeCategoryUsage(): Map<string, number> {
    const categoryUsage = new Map<string, number>();
    
    // 查询所有几何元素及其类别
    const sql = `
      SELECT Category.Id, COUNT(*) as ElementCount 
      FROM BisCore:GeometricElement 
      GROUP BY Category.Id
    `;
    
    this.sourceDb.withPreparedStatement(sql, (statement) => {
      while (statement.step() === DbResult.BE_SQLITE_ROW) {
        const categoryId = statement.getValue(0).getId();
        const elementCount = statement.getValue(1).getInteger();
        categoryUsage.set(categoryId, elementCount);
      }
    });
    
    return categoryUsage;
  }
  
  public getUnusedCategories(): string[] {
    const allCategories = new Set<string>();
    const usedCategories = new Set<string>();
    
    // 获取所有类别
    const categorySql = `SELECT ECInstanceId FROM BisCore:SpatialCategory`;
    this.sourceDb.withPreparedStatement(categorySql, (statement) => {
      while (statement.step() === DbResult.BE_SQLITE_ROW) {
        allCategories.add(statement.getValue(0).getId());
      }
    });
    
    // 获取使用的类别
    const usageSql = `SELECT DISTINCT Category.Id FROM BisCore:GeometricElement`;
    this.sourceDb.withPreparedStatement(usageSql, (statement) => {
      while (statement.step() === DbResult.BE_SQLITE_ROW) {
        usedCategories.add(statement.getValue(0).getId());
      }
    });
    
    // 返回未使用的类别
    return Array.from(allCategories).filter(cat => !usedCategories.has(cat));
  }
  
  public excludeUnusedCategories(exporter: IModelExporter): void {
    const unusedCategories = this.getUnusedCategories();
    unusedCategories.forEach(categoryId => {
      exporter.excludeElementsInCategory(categoryId);
    });
    
    console.log(`Excluded ${unusedCategories.length} unused categories`);
  }
}

// 使用示例
const analyzer = new CategoryAnalyzer(sourceDb);
const usage = analyzer.analyzeCategoryUsage();
console.log("Category usage:", usage);

analyzer.excludeUnusedCategories(exporter);
```

## 测试示例

### 7. 单元测试示例

```typescript
import { expect } from "chai";
import { IModelExporter } from "@itwin/transformer";

describe("Category Export Tests", () => {
  let sourceDb: SnapshotDb;
  let exporter: IModelExporter;
  
  beforeEach(() => {
    sourceDb = SnapshotDb.createEmpty("test.bim", {
      rootSubject: { name: "Test" }
    });
    exporter = new IModelExporter(sourceDb);
  });
  
  it("should exclude elements in specified category", () => {
    // 创建测试类别
    const categoryId = SpatialCategory.insert(
      sourceDb,
      IModel.dictionaryId,
      "TestCategory",
      new SubCategoryAppearance()
    );
    
    // 创建测试元素
    const modelId = PhysicalModel.insert(
      sourceDb,
      IModel.rootSubjectId,
      "TestModel"
    );
    
    const elementProps: PhysicalElementProps = {
      classFullName: PhysicalObject.classFullName,
      model: modelId,
      category: categoryId,
      code: Code.createEmpty(),
    };
    
    const elementId = sourceDb.elements.insertElement(elementProps);
    
    // 排除类别
    exporter.excludeElementsInCategory(categoryId);
    
    // 验证元素被排除
    const element = sourceDb.elements.getElement(elementId);
    expect(exporter.shouldExportElement(element)).to.be.false;
  });
  
  it("should handle multiple category exclusions", () => {
    const categoryIds = [];
    
    // 创建多个类别
    for (let i = 0; i < 3; i++) {
      const categoryId = SpatialCategory.insert(
        sourceDb,
        IModel.dictionaryId,
        `TestCategory${i}`,
        new SubCategoryAppearance()
      );
      categoryIds.push(categoryId);
      exporter.excludeElementsInCategory(categoryId);
    }
    
    // 验证所有类别都被排除
    categoryIds.forEach(categoryId => {
      // 这里需要创建测试元素来验证
      // 具体实现略...
    });
  });
});
```

### 8. 集成测试示例

```typescript
describe("Category Export Integration Tests", () => {
  it("should export only selected categories", async () => {
    // 设置源数据库
    const sourceDb = SnapshotDb.openFile("source-with-categories.bim");
    const targetDb = SnapshotDb.createEmpty("target.bim", {
      rootSubject: { name: "Target" }
    });
    
    // 配置导出器
    const exporter = new IModelExporter(sourceDb);
    
    // 只保留特定类别
    const keepCategories = ["Architecture", "Structure"];
    const allCategories = getAllCategoryNames(sourceDb);
    
    allCategories.forEach(categoryName => {
      if (!keepCategories.includes(categoryName)) {
        const categoryId = getCategoryIdByName(sourceDb, categoryName);
        if (categoryId) {
          exporter.excludeElementsInCategory(categoryId);
        }
      }
    });
    
    // 执行导出
    await exporter.exportAll();
    
    // 验证结果
    const exportedCategories = getAllCategoryNames(targetDb);
    expect(exportedCategories).to.have.members(keepCategories);
  });
});

// 辅助函数
function getAllCategoryNames(db: SnapshotDb): string[] {
  const names: string[] = [];
  const sql = `SELECT CodeValue FROM BisCore:SpatialCategory`;
  
  db.withPreparedStatement(sql, (statement) => {
    while (statement.step() === DbResult.BE_SQLITE_ROW) {
      names.push(statement.getValue(0).getString());
    }
  });
  
  return names;
}

function getCategoryIdByName(db: SnapshotDb, name: string): string | undefined {
  const code = SpatialCategory.createCode(db, IModel.dictionaryId, name);
  return db.elements.queryElementIdByCode(code);
}
```

## 性能优化示例

### 9. 批量操作优化

```typescript
class OptimizedCategoryExporter {
  private exporter: IModelExporter;
  private excludedCategories = new Set<string>();
  
  constructor(exporter: IModelExporter) {
    this.exporter = exporter;
  }
  
  // 批量添加排除规则（延迟应用）
  public addCategoryExclusion(categoryId: string): void {
    this.excludedCategories.add(categoryId);
  }
  
  // 一次性应用所有排除规则
  public applyExclusions(): void {
    this.excludedCategories.forEach(categoryId => {
      this.exporter.excludeElementsInCategory(categoryId);
    });
    
    console.log(`Applied ${this.excludedCategories.size} category exclusions`);
    this.excludedCategories.clear();
  }
  
  // 基于条件的智能排除
  public excludeCategoriesByPattern(pattern: RegExp): void {
    const sql = `SELECT ECInstanceId, CodeValue FROM BisCore:SpatialCategory`;
    
    this.exporter.sourceDb.withPreparedStatement(sql, (statement) => {
      while (statement.step() === DbResult.BE_SQLITE_ROW) {
        const categoryId = statement.getValue(0).getId();
        const categoryName = statement.getValue(1).getString();
        
        if (pattern.test(categoryName)) {
          this.addCategoryExclusion(categoryId);
        }
      }
    });
    
    this.applyExclusions();
  }
}

// 使用示例
const optimizer = new OptimizedCategoryExporter(exporter);

// 排除所有临时和测试类别
optimizer.excludeCategoriesByPattern(/^(temp|test|debug)/i);

// 排除所有以数字结尾的类别
optimizer.excludeCategoriesByPattern(/\d+$/);
```

这些示例展示了 ExportCategories 功能的各种使用场景，从基础的类别排除到高级的自定义过滤策略，以及性能优化技巧。
