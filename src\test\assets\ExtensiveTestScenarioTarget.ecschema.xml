﻿<?xml version="1.0" encoding="UTF-8"?>
<ECSchema schemaName="ExtensiveTestScenarioTarget" alias="target" version="01.00.00" xmlns="http://www.bentley.com/schemas/Bentley.ECXML.3.1" description="The target schema for IModelTransformer tests">

    <ECSchemaReference name="BisCore" version="01.00.04" alias="bis"/>

    <ECEntityClass typeName="TargetPhysicalElement" modifier="Sealed">
        <BaseClass>bis:PhysicalElement</BaseClass>
        <ECProperty propertyName="TargetString" typeName="string" description="A string property that only exists in the target schema"/>
        <ECProperty propertyName="TargetDouble" typeName="double" description="A double property that only exists in the target schema"/>
        <ECProperty propertyName="TargetBinary" typeName="binary" description="A binary property that only exists in the target schema"/>
        <ECNavigationProperty propertyName="TargetNavigation" relationshipName="TargetPhysicalElementUsesTargetDefinition" direction="Forward" description="A NavigationProperty that only exists in the target schema"/>
        <ECProperty propertyName="CommonString" typeName="string" description="A string property that exists in both schemas"/>
        <ECProperty propertyName="CommonDouble" typeName="double" description="A double property that exists in both schemas"/>
        <ECProperty propertyName="CommonBinary" typeName="binary" description="A binary property that exists in both schemas"/>
        <ECNavigationProperty propertyName="CommonNavigation" relationshipName="TargetPhysicalElementUsesCommonDefinition" direction="Forward" description="A NavigationProperty that exists in both schemas"/>
    </ECEntityClass>

    <ECRelationshipClass typeName="TargetPhysicalElementUsesTargetDefinition" strength="referencing" modifier="None">
        <Source multiplicity="(0..*)" roleLabel="uses" polymorphic="true">
            <Class class="TargetPhysicalElement" />
        </Source>
        <Target multiplicity="(0..1)" roleLabel="is used by" polymorphic="true">
            <Class class="bis:DefinitionElement"/>
        </Target>
    </ECRelationshipClass>

    <ECRelationshipClass typeName="TargetPhysicalElementUsesCommonDefinition" strength="referencing" modifier="Sealed">
        <Source multiplicity="(0..*)" roleLabel="uses" polymorphic="true">
            <Class class="TargetPhysicalElement" />
        </Source>
        <Target multiplicity="(0..1)" roleLabel="is used by" polymorphic="true">
            <Class class="bis:DefinitionElement"/>
        </Target>
    </ECRelationshipClass>

    <ECRelationshipClass typeName="TargetRelWithProps" strength="referencing" modifier="None" description="For testing relationship property remapping.">
        <BaseClass>bis:ElementRefersToElements</BaseClass>
        <Source multiplicity="(0..*)" roleLabel="refers to" polymorphic="true">
            <Class class="bis:Element"/>
        </Source>
        <Target multiplicity="(0..*)" roleLabel="is referenced by" polymorphic="true">
            <Class class="bis:Element"/>
        </Target>
        <ECProperty propertyName="TargetString" typeName="string" description="A string property that should be remapped"/>
        <ECProperty propertyName="TargetDouble" typeName="double" description="A double property that should be remapped"/>
        <ECProperty propertyName="TargetLong" typeName="long" extendedTypeName="Id" description="A long property that should be remapped"/>
        <ECProperty propertyName="TargetGuid" typeName="binary" extendedTypeName="BeGuid" description="A GUID property that should be remapped"/>
    </ECRelationshipClass>

    <ECEntityClass typeName="TargetUniqueAspect" description="For testing transformation of SourceUniqueAspect --> TargetUniqueAspect.">
        <BaseClass>bis:ElementUniqueAspect</BaseClass>
        <ECProperty propertyName="CommonDouble" typeName="double" description="A double property that exists on both SourceUniqueAspect and TargetUniqueAspect"/>
        <ECProperty propertyName="CommonString" typeName="string" description="A string property that exists on both SourceUniqueAspect and TargetUniqueAspect"/>
        <ECProperty propertyName="CommonLong" typeName="long" extendedTypeName="Id" description="A long property that exists on both SourceUniqueAspect and TargetUniqueAspect"/>
        <ECProperty propertyName="CommonBinary" typeName="binary" description="A binary property that exists on both SourceUniqueAspect and TargetUniqueAspect"/>
        <ECProperty propertyName="TargetDouble" typeName="double" description="A double property that should be remapped"/>
        <ECProperty propertyName="TargetString" typeName="string" description="A string property that should be remapped"/>
        <ECProperty propertyName="TargetLong" typeName="long" extendedTypeName="Id" description="A long property that should be remapped"/>
        <ECProperty propertyName="TargetGuid" typeName="binary" extendedTypeName="BeGuid" description="A GUID property that should be remapped"/>
    </ECEntityClass>

    <ECEntityClass typeName="TargetMultiAspect" description="For testing transformation of SourceMultiAspect --> TargetMultiAspect.">
        <BaseClass>bis:ElementMultiAspect</BaseClass>
        <ECProperty propertyName="CommonDouble" typeName="double" description="A double property that exists on both SourceMultiAspect and TargetMultiAspect"/>
        <ECProperty propertyName="CommonString" typeName="string" description="A string property that exists on both SourceMultiAspect and TargetMultiAspect"/>
        <ECProperty propertyName="CommonLong" typeName="long" extendedTypeName="Id" description="A long property that exists on both SourceMultiAspect and TargetMultiAspect"/>
        <ECProperty propertyName="CommonBinary" typeName="binary" description="A binary property that exists on both SourceMultiAspect and TargetMultiAspect"/>
        <ECProperty propertyName="TargetDouble" typeName="double" description="A double property that should be remapped"/>
        <ECProperty propertyName="TargetString" typeName="string" description="A string property that should be remapped"/>
        <ECProperty propertyName="TargetLong" typeName="long" extendedTypeName="Id" description="A long property that should be remapped"/>
        <ECProperty propertyName="TargetGuid" typeName="binary" extendedTypeName="BeGuid" description="A GUID property that should be remapped"/>
    </ECEntityClass>

    <ECEntityClass typeName="TargetInformationRecord" modifier="Sealed">
        <BaseClass>bis:InformationRecordElement</BaseClass>
        <ECProperty propertyName="CommonString" typeName="string" description="A string property that exists on both SourceInformationRecord and TargetInformationRecord"/>
        <ECProperty propertyName="TargetString" typeName="string" description="A string property that should be remapped"/>
    </ECEntityClass>

    <ECRelationshipClass typeName="PhysicalPartitionIsTrackedByRecords" strength="referencing" modifier="Sealed" description="Relates a PhysicalPartition to the InformationRecordPartition that tracks it.">
        <BaseClass>bis:ElementRefersToElements</BaseClass>
        <Source multiplicity="(0..1)" roleLabel="is tracked by" polymorphic="true">
            <Class class="bis:PhysicalPartition"/>
        </Source>
        <Target multiplicity="(0..1)" roleLabel="tracks" polymorphic="true">
            <Class class="bis:InformationRecordPartition"/>
        </Target>
    </ECRelationshipClass>

    <ECEntityClass typeName="AuditRecord" modifier="Sealed">
        <BaseClass>bis:InformationRecordElement</BaseClass>
        <ECProperty propertyName="Operation" typeName="string"/>
        <ECNavigationProperty propertyName="PhysicalElement" relationshipName="AuditRecordTracksPhysicalElement" direction="Forward">
            <!-- AuditRecord elements are not deleted, so PhysicalElement.Id will become invalid when the PhysicalElement is deleted. -->
        </ECNavigationProperty>
    </ECEntityClass>

    <ECRelationshipClass typeName="AuditRecordTracksPhysicalElement" strength="referencing" modifier="Sealed">
        <Source multiplicity="(0..*)" roleLabel="tracks" polymorphic="true">
            <Class class="AuditRecord" />
        </Source>
        <Target multiplicity="(0..1)" roleLabel="is tracked by" polymorphic="true">
            <Class class="bis:PhysicalElement"/>
        </Target>
    </ECRelationshipClass>

</ECSchema>
