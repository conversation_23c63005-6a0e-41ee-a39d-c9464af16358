# ExportCategories 功能分析文档

## 概述

在 iModel Transformer 中，类别（Categories）的导出功能是通过多个组件协同工作实现的，而不是通过单一的 `ExportCategories` 类。本文档详细分析了类别导出相关的代码结构、流程和使用方法。

## 核心组件

### 1. IModelExporter 中的类别处理

#### 类别排除机制

`IModelExporter` 类提供了排除特定类别中所有元素的功能：

```typescript
/** The set of Categories where Elements in that Category will be excluded from transformation to the target iModel. */
private _excludedElementCategoryIds = new Set<Id64String>();

/** Add a rule to exclude all Elements in a specified Category. */
public excludeElementsInCategory(categoryId: Id64String): void {
  this._excludedElementCategoryIds.add(categoryId);
}
```

#### 类别过滤逻辑

在 `shouldExportElement()` 方法中实现类别过滤：

```typescript
public shouldExportElement(element: Element): boolean {
  if (this._excludedElementIds.has(element.id)) {
    Logger.logInfo(loggerCategory, `Excluded element ${element.id} by Id`);
    return false;
  }
  if (element instanceof GeometricElement) {
    if (this._excludedElementCategoryIds.has(element.category)) {
      Logger.logInfo(
        loggerCategory,
        `Excluded element ${element.id} by Category`
      );
      return false;
    }
  }
  // ... 其他过滤逻辑
}
```

### 2. 类别选择器（CategorySelector）导出

#### 测试用例中的类别选择器创建

在测试代码中展示了如何创建和使用类别选择器进行导出：

```typescript
const exportCategorySelectorId = CategorySelector.insert(
  sourceDb,
  IModel.dictionaryId,
  "Export",
  [
    sourceDb.elements.queryElementIdByCode(
      SpatialCategory.createCode(
        sourceDb,
        IModel.dictionaryId,
        categoryNames[0]
      )
    )!,
    sourceDb.elements.queryElementIdByCode(
      SpatialCategory.createCode(
        sourceDb,
        IModel.dictionaryId,
        categoryNames[2]
      )
    )!,
    // ... 更多类别
  ]
);
```

### 3. 基于视图的类别过滤

#### FilterByViewTransformer 实现

在 `IModelTransformerUtils.ts` 中的 `FilterByViewTransformer` 类展示了基于视图定义进行类别过滤的高级用法：

```typescript
/** Excludes categories not referenced by the export view's CategorySelector */
private excludeCategoriesExcept(exportCategoryIds: Id64Set): void {
  const sql = `SELECT ECInstanceId FROM ${SpatialCategory.classFullName}`;
  this.sourceDb.withPreparedStatement(
    sql,
    (statement: ECSqlStatement): void => {
      while (DbResult.BE_SQLITE_ROW === statement.step()) {
        const categoryId = statement.getValue(0).getId();
        if (!exportCategoryIds.has(categoryId)) {
          this.exporter.excludeElementsInCategory(categoryId);
        }
      }
    }
  );
}
```

## 类别导出流程

### 1. 初始化阶段

1. 创建 `IModelExporter` 实例
2. 配置类别排除规则（如果需要）
3. 设置导出处理器

### 2. 类别过滤配置

有多种方式配置类别过滤：

#### 方式一：直接排除特定类别
```typescript
exporter.excludeElementsInCategory(categoryId);
```

#### 方式二：基于类别选择器过滤
```typescript
const categorySelector = sourceDb.elements.getElement<CategorySelector>(
  categorySelectorId,
  CategorySelector
);
// 排除不在选择器中的类别
```

#### 方式三：基于视图定义过滤
```typescript
const viewDefinition = sourceDb.elements.getElement<ViewDefinition>(viewId);
const categorySelector = sourceDb.elements.getElement<CategorySelector>(
  viewDefinition.categorySelectorId
);
// 使用视图的类别选择器进行过滤
```

### 3. 导出执行

1. 调用 `exportAll()` 或 `exportModel()` 等方法
2. 对每个元素调用 `shouldExportElement()` 进行过滤
3. 符合条件的元素被导出到目标模型

## 类别重映射

### Catalog 测试中的类别重映射

在 `Catalog.test.ts` 中展示了类别重映射的实现：

```typescript
private _remapSpatialCategories(): void {
  if (
    undefined === this._targetSpatialCategories ||
    this._targetSpatialCategories.size === 0
  ) {
    return;
  }
  // 实现空间类别重映射逻辑
}

private _remapDrawingCategories(): void {
  // 实现绘图类别重映射逻辑
}
```

## 使用示例

### 基本类别排除示例

```typescript
import { IModelExporter } from "@itwin/transformer";

const exporter = new IModelExporter(sourceDb);

// 排除特定类别的所有元素
const categoryToExclude = "unwanted-category-id";
exporter.excludeElementsInCategory(categoryToExclude);

// 执行导出
await exporter.exportAll();
```

### 基于视图的选择性导出

```typescript
import { FilterByViewTransformer } from "./test/IModelTransformerUtils";

const transformer = new FilterByViewTransformer(
  sourceDb,
  targetDb,
  exportViewId
);

// 自动根据视图的类别选择器过滤类别
await transformer.process();
```

## 注意事项

1. **几何元素过滤**：只有 `GeometricElement` 实例才会应用类别过滤
2. **性能考虑**：大量类别排除规则可能影响导出性能
3. **日志记录**：所有类别过滤操作都会记录到日志中
4. **类别依赖**：确保不要排除被其他必需元素引用的类别

## 相关文件

- `src/IModelExporter.ts` - 主要的导出器实现
- `src/test/IModelTransformerUtils.ts` - 测试工具和高级过滤器
- `src/test/standalone/Catalog.test.ts` - 类别重映射示例
- `src/test/standalone/IModelTransformer.test.ts` - 类别选择器使用示例

## API 参考

### IModelExporter 类别相关方法

- `excludeElementsInCategory(categoryId: Id64String): void` - 排除指定类别中的所有元素
- `shouldExportElement(element: Element): boolean` - 判断元素是否应该被导出（包含类别过滤逻辑）

### 相关类型

- `Id64String` - 类别 ID 类型
- `GeometricElement` - 具有类别属性的几何元素基类
- `CategorySelector` - 类别选择器类
- `SpatialCategory` - 空间类别类
- `DrawingCategory` - 绘图类别类
