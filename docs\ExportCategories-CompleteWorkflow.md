# Category 导出完整流程分析

## 概述

本文档详细分析了 iModel Transformer 中 Category 导出的完整工作流程，包括从初始化到最终导出的每个步骤，以及如何在 iModelNative (C++) 环境中实现相同的功能。

## 完整导出流程图

```mermaid
graph TD
    A[开始导出] --> B[初始化 IModelExporter]
    B --> C[注册 IModelExportHandler]
    C --> D[配置类别过滤规则]
    D --> E[调用 exportAll 或 exportChanges]
    
    E --> F[导出 CodeSpecs]
    F --> G[导出 Fonts]
    G --> H[导出 Repository Model]
    
    H --> I[exportModel 流程]
    I --> J[检查模型是否为模板]
    J --> K{shouldExportElement 检查}
    K -->|通过| L[导出模型容器]
    K -->|拒绝| M[跳过模型]
    
    L --> N[exportModelContents]
    N --> O[查询顶级元素]
    O --> P[遍历每个元素]
    
    P --> Q[exportElement 流程]
    Q --> R{元素 ID 排除检查}
    R -->|排除| S[调用 onSkipElement]
    R -->|通过| T{几何元素类别检查}
    
    T -->|排除| S
    T -->|通过| U{元素类排除检查}
    U -->|排除| S
    U -->|通过| V{自定义过滤器检查}
    
    V -->|排除| S
    V -->|通过| W[调用 preExportElement]
    W --> X[调用 onExportElement]
    X --> Y[导出元素方面]
    Y --> Z[导出子元素]
    
    Z --> AA[exportChildElements]
    AA --> AB[递归调用 exportElement]
    
    AB --> AC[exportSubModels]
    AC --> AD[递归导出子模型]
    
    AD --> AE[导出关系]
    AE --> AF[完成导出]
    
    M --> AC
    S --> P
```

## 详细流程分析

### 1. 初始化阶段

#### 1.1 创建导出器
```typescript
// 位置: src/IModelExporter.ts:333-353
public constructor(
  sourceDb: IModelDb,
  elementAspectsStrategy: new (
    source: IModelDb,
    handler: ElementAspectsHandler
  ) => ExportElementAspectsStrategy = ExportElementAspectsWithElementsStrategy
) {
  this.sourceDb = sourceDb;
  this._exportElementAspectsStrategy = new elementAspectsStrategy(
    this.sourceDb,
    {
      onExportElementMultiAspects: (aspects) =>
        this.handler.onExportElementMultiAspects(aspects),
      onExportElementUniqueAspect: (aspect, isUpdate) =>
        this.handler.onExportElementUniqueAspect(aspect, isUpdate),
      shouldExportElementAspect: (aspect) =>
        this.handler.shouldExportElementAspect(aspect),
      trackProgress: async () => this.trackProgress(),
    }
  );
}
```

#### 1.2 注册处理器
```typescript
// 位置: src/IModelExporter.ts:377-379
public registerHandler(handler: IModelExportHandler): void {
  this._handler = handler;
}
```

#### 1.3 配置类别过滤
```typescript
// 位置: src/IModelExporter.ts:392-394
public excludeElementsInCategory(categoryId: Id64String): void {
  this._excludedElementCategoryIds.add(categoryId);
}
```

### 2. 主导出流程

#### 2.1 exportAll 方法
```typescript
// 位置: src/IModelExporter.ts:418-425
public async exportAll(): Promise<void> {
  await this.initialize({});

  await this.exportCodeSpecs();      // 导出代码规范
  await this.exportFonts();          // 导出字体
  await this.exportModel(IModel.repositoryModelId);  // 导出根模型
  await this.exportRelationships(ElementRefersToElements.classFullName);  // 导出关系
}
```

#### 2.2 exportModel 方法
```typescript
// 位置: src/IModelExporter.ts:670-688
public async exportModel(modeledElementId: Id64String): Promise<void> {
  const model: Model = this.sourceDb.models.getModel(modeledElementId);
  if (model.isTemplate && !this.wantTemplateModels) {
    return;  // 跳过模板模型
  }
  
  const modeledElement: Element = this.sourceDb.elements.getElement({
    id: modeledElementId,
    wantGeometry: this.wantGeometry,
    wantBRepData: this.wantGeometry,
  });
  
  Logger.logTrace(loggerCategory, `exportModel(${modeledElementId})`);
  
  if (this.shouldExportElement(modeledElement)) {
    await this.exportModelContainer(model);      // 导出模型容器
    if (this.visitElements) {
      await this.exportModelContents(modeledElementId);  // 导出模型内容
    }
    await this.exportSubModels(modeledElementId);  // 导出子模型
  }
}
```

### 3. 类别过滤核心逻辑

#### 3.1 shouldExportElement 方法
```typescript
// 位置: src/IModelExporter.ts:803-838
public shouldExportElement(element: Element): boolean {
  // 1. 检查元素 ID 排除列表
  if (this._excludedElementIds.has(element.id)) {
    Logger.logInfo(loggerCategory, `Excluded element ${element.id} by Id`);
    return false;
  }
  
  // 2. 检查几何元素的类别排除（核心类别过滤逻辑）
  if (element instanceof GeometricElement) {
    if (this._excludedElementCategoryIds.has(element.category)) {
      Logger.logInfo(
        loggerCategory,
        `Excluded element ${element.id} by Category`
      );
      return false;
    }
  }
  
  // 3. 检查模板模型排除
  if (
    !this.wantTemplateModels &&
    element instanceof RecipeDefinitionElement
  ) {
    Logger.logInfo(
      loggerCategory,
      `Excluded RecipeDefinitionElement ${element.id} because wantTemplate=false`
    );
    return false;
  }
  
  // 4. 检查元素类排除
  for (const excludedElementClass of this._excludedElementClasses) {
    if (element instanceof excludedElementClass) {
      Logger.logInfo(
        loggerCategory,
        `Excluded element ${element.id} by class: ${excludedElementClass.classFullName}`
      );
      return false;
    }
  }
  
  // 5. 调用自定义处理器过滤
  return this.handler.shouldExportElement(element);
}
```

### 4. 元素导出流程

#### 4.1 exportElement 方法
```typescript
// 位置: src/IModelExporter.ts:843-889
public async exportElement(elementId: Id64String): Promise<void> {
  if (!this.visitElements) {
    Logger.logTrace(
      loggerCategory,
      `visitElements=false, skipping exportElement(${elementId})`
    );
    return;
  }

  // 早期 ID 检查优化
  if (this._excludedElementIds.has(elementId)) {
    Logger.logInfo(loggerCategory, `Excluded element ${elementId} by Id`);
    this.handler.onSkipElement(elementId);
    return;
  }

  // 加载元素
  const element: Element = this.sourceDb.elements.getElement({
    id: elementId,
    wantGeometry: this.wantGeometry,
    wantBRepData: this.wantGeometry,
  });
  
  Logger.logTrace(
    loggerCategory,
    `exportElement(${element.id}, "${element.getDisplayLabel()}")${this.getChangeOpSuffix(isUpdate)}`
  );
  
  // 应用过滤逻辑
  if (this.shouldExportElement(element)) {
    await this.handler.preExportElement(element);
    this.handler.onExportElement(element, isUpdate);
    await this.trackProgress();
    await this._exportElementAspectsStrategy.exportElementAspectsForElement(elementId);
    return this.exportChildElements(elementId);
  } else {
    this.handler.onSkipElement(element.id);
  }
}
```

### 5. 模型内容导出

#### 5.1 exportModelContents 方法
```typescript
// 位置: src/IModelExporter.ts:715-763
public async exportModelContents(
  modelId: Id64String,
  elementClassFullName: string = Element.classFullName,
  skipRootSubject?: boolean
): Promise<void> {
  if (!this.visitElements) {
    Logger.logTrace(
      loggerCategory,
      `visitElements=false, skipping exportModelContents(${modelId})`
    );
    return;
  }
  
  Logger.logTrace(loggerCategory, `exportModelContents(${modelId})`);
  
  // 构建查询 SQL
  let sql: string;
  if (skipRootSubject) {
    sql = `SELECT ECInstanceId FROM ${elementClassFullName} WHERE Parent.Id IS NULL AND Model.Id=:modelId AND ECInstanceId!=:rootSubjectId ORDER BY ECInstanceId`;
  } else {
    sql = `SELECT ECInstanceId FROM ${elementClassFullName} WHERE Parent.Id IS NULL AND Model.Id=:modelId ORDER BY ECInstanceId`;
  }
  
  // 执行查询并导出每个顶级元素
  await this.sourceDb.withPreparedStatement(
    sql,
    async (statement: ECSqlStatement): Promise<void> => {
      statement.bindId("modelId", modelId);
      if (skipRootSubject) {
        statement.bindId("rootSubjectId", IModel.rootSubjectId);
      }
      while (DbResult.BE_SQLITE_ROW === statement.step()) {
        await this.exportElement(statement.getValue(0).getId());
        await this._yieldManager.allowYield();
      }
    }
  );
}
```

## 类别过滤的关键时机

### 1. 预过滤阶段
- **位置**: `excludeElementsInCategory()` 调用时
- **作用**: 将类别 ID 添加到排除集合中
- **性能**: O(1) 操作

### 2. 元素加载前过滤
- **位置**: `exportElement()` 方法开始
- **作用**: 通过元素 ID 快速排除
- **性能**: 避免不必要的数据库查询

### 3. 元素加载后过滤
- **位置**: `shouldExportElement()` 方法
- **作用**: 基于元素属性（包括类别）进行过滤
- **性能**: 需要加载元素但避免后续处理

### 4. 自定义过滤
- **位置**: `IModelExportHandler.shouldExportElement()`
- **作用**: 用户自定义的复杂过滤逻辑
- **性能**: 取决于用户实现

## 性能优化策略

### 1. 数据结构优化
```typescript
// 使用 Set 而不是 Array 进行快速查找
private _excludedElementCategoryIds = new Set<Id64String>();
```

### 2. 早期退出策略
```typescript
// 在加载元素前就检查 ID 排除
if (this._excludedElementIds.has(elementId)) {
  this.handler.onSkipElement(elementId);
  return;
}
```

### 3. 批量查询优化
```typescript
// 使用 ECSQL 批量查询顶级元素
const sql = `SELECT ECInstanceId FROM ${elementClassFullName} WHERE Parent.Id IS NULL AND Model.Id=:modelId ORDER BY ECInstanceId`;
```

### 4. 异步处理和让步
```typescript
// 允许事件循环处理其他任务
await this._yieldManager.allowYield();
```

## 错误处理和日志

### 1. 详细日志记录
```typescript
Logger.logInfo(loggerCategory, `Excluded element ${element.id} by Category`);
Logger.logTrace(loggerCategory, `exportElement(${element.id})`);
```

### 2. 优雅的错误处理
- 跳过无效元素而不是抛出异常
- 记录排除操作以便调试
- 继续处理其他元素

### 3. 进度跟踪
```typescript
await this.trackProgress();
```

这个完整的流程展示了 Category 导出功能如何与整个 iModel 导出系统集成，以及类别过滤在各个阶段的作用机制。

---

# iModelNative (C++) 实现方案

## 架构对比

### TypeScript 架构
```
Application Layer (TypeScript)
    ↓
iTwin.js Backend Framework (TypeScript)
    ↓
JavaScript Runtime with C++ Interoperability (Node.js/V8)
    ↓
iTwin.js Native Libraries (C++)
```

### iModelNative 架构
```
Application Layer (C++)
    ↓
iModel Native SDK (C++)
    ↓
SQLite/DGN Libraries (C++)
```

## 核心组件 C++ 实现

### 1. IModelExporter C++ 类设计

```cpp
// IModelExporter.h
#pragma once
#include <memory>
#include <unordered_set>
#include <string>
#include <functional>
#include "IModelDb.h"
#include "Element.h"
#include "Model.h"

namespace BentleyApi {
namespace iModel {

class IModelExportHandler;

class IModelExporter {
public:
    // 构造函数
    explicit IModelExporter(IModelDbR sourceDb);
    virtual ~IModelExporter() = default;

    // 注册处理器
    void RegisterHandler(std::unique_ptr<IModelExportHandler> handler);

    // 类别过滤方法
    void ExcludeElementsInCategory(BeInt64Id categoryId);
    bool ShouldExportElement(ElementCR element) const;

    // 主导出方法
    BentleyStatus ExportAll();
    BentleyStatus ExportModel(BeInt64Id modeledElementId);
    BentleyStatus ExportElement(BeInt64Id elementId);

    // 配置选项
    void SetWantGeometry(bool want) { m_wantGeometry = want; }
    void SetVisitElements(bool visit) { m_visitElements = visit; }
    void SetVisitRelationships(bool visit) { m_visitRelationships = visit; }

private:
    // 内部实现方法
    BentleyStatus ExportModelContainer(ModelCR model);
    BentleyStatus ExportModelContents(BeInt64Id modelId,
                                     Utf8CP elementClassFullName = nullptr);
    BentleyStatus ExportSubModels(BeInt64Id parentModelId);
    BentleyStatus ExportChildElements(BeInt64Id elementId);

    // 成员变量
    IModelDbR m_sourceDb;
    std::unique_ptr<IModelExportHandler> m_handler;
    std::unordered_set<BeInt64Id> m_excludedElementCategoryIds;
    std::unordered_set<BeInt64Id> m_excludedElementIds;
    bool m_wantGeometry = true;
    bool m_visitElements = true;
    bool m_visitRelationships = true;
};

// 导出处理器基类
class IModelExportHandler {
public:
    virtual ~IModelExportHandler() = default;

    // 虚方法 - 子类可重写
    virtual bool ShouldExportElement(ElementCR element) { return true; }
    virtual void OnExportElement(ElementCR element, bool isUpdate) {}
    virtual void OnSkipElement(BeInt64Id elementId) {}
    virtual void OnExportModel(ModelCR model, bool isUpdate) {}
    virtual bool ShouldExportCodeSpec(CodeSpecCR codeSpec) { return true; }
    virtual void OnExportCodeSpec(CodeSpecCR codeSpec, bool isUpdate) {}
};

} // namespace iModel
} // namespace BentleyApi
```

### 2. 核心实现

```cpp
// IModelExporter.cpp
#include "IModelExporter.h"
#include "GeometricElement.h"
#include "Logger.h"

namespace BentleyApi {
namespace iModel {

IModelExporter::IModelExporter(IModelDbR sourceDb)
    : m_sourceDb(sourceDb) {
}

void IModelExporter::RegisterHandler(std::unique_ptr<IModelExportHandler> handler) {
    m_handler = std::move(handler);
}

void IModelExporter::ExcludeElementsInCategory(BeInt64Id categoryId) {
    m_excludedElementCategoryIds.insert(categoryId);
}

bool IModelExporter::ShouldExportElement(ElementCR element) const {
    // 1. 检查元素 ID 排除列表
    if (m_excludedElementIds.find(element.GetElementId()) != m_excludedElementIds.end()) {
        LOG.infov("Excluded element %s by Id", element.GetElementId().ToString().c_str());
        return false;
    }

    // 2. 检查几何元素的类别排除
    auto geometricElement = dynamic_cast<GeometricElementCP>(&element);
    if (geometricElement != nullptr) {
        BeInt64Id categoryId = geometricElement->GetCategoryId();
        if (m_excludedElementCategoryIds.find(categoryId) != m_excludedElementCategoryIds.end()) {
            LOG.infov("Excluded element %s by Category %s",
                     element.GetElementId().ToString().c_str(),
                     categoryId.ToString().c_str());
            return false;
        }
    }

    // 3. 调用自定义处理器过滤
    if (m_handler) {
        return m_handler->ShouldExportElement(element);
    }

    return true;
}

BentleyStatus IModelExporter::ExportAll() {
    if (!m_handler) {
        LOG.error("IModelExportHandler not registered");
        return ERROR;
    }

    // 导出所有实体类型
    auto status = ExportModel(m_sourceDb.Elements().GetRootSubject()->GetElementId());
    if (SUCCESS != status) {
        return status;
    }

    // 可以添加其他导出步骤（CodeSpecs, Fonts, Relationships等）
    return SUCCESS;
}

BentleyStatus IModelExporter::ExportModel(BeInt64Id modeledElementId) {
    // 获取模型
    ModelPtr model = m_sourceDb.Models().GetModel(modeledElementId);
    if (!model.IsValid()) {
        LOG.errorv("Model not found: %s", modeledElementId.ToString().c_str());
        return ERROR;
    }

    // 获取建模元素
    ElementPtr modeledElement = m_sourceDb.Elements().GetElement(modeledElementId);
    if (!modeledElement.IsValid()) {
        LOG.errorv("Modeled element not found: %s", modeledElementId.ToString().c_str());
        return ERROR;
    }

    LOG.tracev("ExportModel(%s)", modeledElementId.ToString().c_str());

    // 检查是否应该导出
    if (!ShouldExportElement(*modeledElement)) {
        return SUCCESS;
    }

    // 导出模型容器
    auto status = ExportModelContainer(*model);
    if (SUCCESS != status) {
        return status;
    }

    // 导出模型内容
    if (m_visitElements) {
        status = ExportModelContents(modeledElementId);
        if (SUCCESS != status) {
            return status;
        }
    }

    // 导出子模型
    return ExportSubModels(modeledElementId);
}

BentleyStatus IModelExporter::ExportModelContainer(ModelCR model) {
    if (m_handler) {
        m_handler->OnExportModel(model, false); // 假设为插入操作
    }
    return SUCCESS;
}

BentleyStatus IModelExporter::ExportModelContents(BeInt64Id modelId, Utf8CP elementClassFullName) {
    if (!m_visitElements) {
        LOG.tracev("visitElements=false, skipping ExportModelContents(%s)",
                  modelId.ToString().c_str());
        return SUCCESS;
    }

    LOG.tracev("ExportModelContents(%s)", modelId.ToString().c_str());

    // 构建查询语句
    Utf8String sql = "SELECT ECInstanceId FROM ";
    if (elementClassFullName != nullptr) {
        sql.append(elementClassFullName);
    } else {
        sql.append("BisCore:Element");
    }
    sql.append(" WHERE Parent.Id IS NULL AND Model.Id=? ORDER BY ECInstanceId");

    // 准备语句
    ECSqlStatement statement;
    if (ECSqlStatus::Success != statement.Prepare(m_sourceDb, sql.c_str())) {
        LOG.errorv("Failed to prepare statement: %s", sql.c_str());
        return ERROR;
    }

    // 绑定参数
    statement.BindId(1, modelId);

    // 执行查询并导出每个元素
    while (BE_SQLITE_ROW == statement.Step()) {
        BeInt64Id elementId = statement.GetValueId<BeInt64Id>(0);
        auto status = ExportElement(elementId);
        if (SUCCESS != status) {
            LOG.errorv("Failed to export element: %s", elementId.ToString().c_str());
            // 继续处理其他元素，不中断整个流程
        }
    }

    return SUCCESS;
}

BentleyStatus IModelExporter::ExportElement(BeInt64Id elementId) {
    if (!m_visitElements) {
        LOG.tracev("visitElements=false, skipping ExportElement(%s)",
                  elementId.ToString().c_str());
        return SUCCESS;
    }

    // 早期 ID 检查优化
    if (m_excludedElementIds.find(elementId) != m_excludedElementIds.end()) {
        LOG.infov("Excluded element %s by Id", elementId.ToString().c_str());
        if (m_handler) {
            m_handler->OnSkipElement(elementId);
        }
        return SUCCESS;
    }

    // 加载元素
    ElementPtr element = m_sourceDb.Elements().GetElement(elementId);
    if (!element.IsValid()) {
        LOG.errorv("Element not found: %s", elementId.ToString().c_str());
        return ERROR;
    }

    LOG.tracev("ExportElement(%s)", elementId.ToString().c_str());

    // 应用过滤逻辑
    if (ShouldExportElement(*element)) {
        if (m_handler) {
            m_handler->OnExportElement(*element, false); // 假设为插入操作
        }

        // 导出子元素
        return ExportChildElements(elementId);
    } else {
        if (m_handler) {
            m_handler->OnSkipElement(elementId);
        }
    }

    return SUCCESS;
}

BentleyStatus IModelExporter::ExportChildElements(BeInt64Id elementId) {
    if (!m_visitElements) {
        LOG.tracev("visitElements=false, skipping ExportChildElements(%s)",
                  elementId.ToString().c_str());
        return SUCCESS;
    }

    // 查询子元素
    Utf8String sql = "SELECT ECInstanceId FROM BisCore:Element WHERE Parent.Id=? ORDER BY ECInstanceId";

    ECSqlStatement statement;
    if (ECSqlStatus::Success != statement.Prepare(m_sourceDb, sql.c_str())) {
        LOG.errorv("Failed to prepare child elements query");
        return ERROR;
    }

    statement.BindId(1, elementId);

    bool hasChildren = false;
    while (BE_SQLITE_ROW == statement.Step()) {
        if (!hasChildren) {
            LOG.tracev("ExportChildElements(%s)", elementId.ToString().c_str());
            hasChildren = true;
        }

        BeInt64Id childElementId = statement.GetValueId<BeInt64Id>(0);
        auto status = ExportElement(childElementId);
        if (SUCCESS != status) {
            LOG.errorv("Failed to export child element: %s", childElementId.ToString().c_str());
            // 继续处理其他子元素
        }
    }

    return SUCCESS;
}

BentleyStatus IModelExporter::ExportSubModels(BeInt64Id parentModelId) {
    LOG.tracev("ExportSubModels(%s)", parentModelId.ToString().c_str());

    // 查询子模型
    Utf8String sql = "SELECT ECInstanceId FROM BisCore:Model WHERE ParentModel.Id=? ORDER BY ECInstanceId";

    ECSqlStatement statement;
    if (ECSqlStatus::Success != statement.Prepare(m_sourceDb, sql.c_str())) {
        LOG.errorv("Failed to prepare sub-models query");
        return ERROR;
    }

    statement.BindId(1, parentModelId);

    // 分别收集定义模型和其他模型
    std::vector<BeInt64Id> definitionModelIds;
    std::vector<BeInt64Id> otherModelIds;

    while (BE_SQLITE_ROW == statement.Step()) {
        BeInt64Id modelId = statement.GetValueId<BeInt64Id>(0);

        // 检查是否为定义模型（这里简化处理，实际需要检查模型类型）
        ModelPtr model = m_sourceDb.Models().GetModel(modelId);
        if (model.IsValid() && model->GetHandler().IsDefinitionModel()) {
            definitionModelIds.push_back(modelId);
        } else {
            otherModelIds.push_back(modelId);
        }
    }

    // 先导出定义模型
    for (BeInt64Id definitionModelId : definitionModelIds) {
        auto status = ExportModel(definitionModelId);
        if (SUCCESS != status) {
            LOG.errorv("Failed to export definition model: %s",
                      definitionModelId.ToString().c_str());
        }
    }

    // 再导出其他模型
    for (BeInt64Id otherModelId : otherModelIds) {
        auto status = ExportModel(otherModelId);
        if (SUCCESS != status) {
            LOG.errorv("Failed to export model: %s", otherModelId.ToString().c_str());
        }
    }

    return SUCCESS;
}

} // namespace iModel
} // namespace BentleyApi
```

### 3. 高级类别过滤器实现

```cpp
// CategoryFilterTransformer.h
#pragma once
#include "IModelExporter.h"
#include <unordered_set>

namespace BentleyApi {
namespace iModel {

// 基于视图的类别过滤器
class ViewBasedCategoryFilter : public IModelExportHandler {
public:
    explicit ViewBasedCategoryFilter(IModelDbR sourceDb, BeInt64Id viewDefinitionId);

    // 重写基类方法
    bool ShouldExportElement(ElementCR element) override;
    void OnExportElement(ElementCR element, bool isUpdate) override;

private:
    void InitializeAllowedCategories();

    IModelDbR m_sourceDb;
    BeInt64Id m_viewDefinitionId;
    std::unordered_set<BeInt64Id> m_allowedCategories;
};

// 自定义类别过滤器
class CustomCategoryFilter : public IModelExportHandler {
public:
    explicit CustomCategoryFilter(std::function<bool(BeInt64Id)> categoryFilter);

    bool ShouldExportElement(ElementCR element) override;
    void AddAllowedCategory(BeInt64Id categoryId);
    void RemoveAllowedCategory(BeInt64Id categoryId);

private:
    std::function<bool(BeInt64Id)> m_categoryFilter;
    std::unordered_set<BeInt64Id> m_allowedCategories;
};

} // namespace iModel
} // namespace BentleyApi
```

```cpp
// CategoryFilterTransformer.cpp
#include "CategoryFilterTransformer.h"
#include "ViewDefinition.h"
#include "CategorySelector.h"
#include "GeometricElement.h"

namespace BentleyApi {
namespace iModel {

ViewBasedCategoryFilter::ViewBasedCategoryFilter(IModelDbR sourceDb, BeInt64Id viewDefinitionId)
    : m_sourceDb(sourceDb), m_viewDefinitionId(viewDefinitionId) {
    InitializeAllowedCategories();
}

void ViewBasedCategoryFilter::InitializeAllowedCategories() {
    // 获取视图定义
    ElementPtr viewElement = m_sourceDb.Elements().GetElement(m_viewDefinitionId);
    if (!viewElement.IsValid()) {
        LOG.errorv("View definition not found: %s", m_viewDefinitionId.ToString().c_str());
        return;
    }

    auto viewDef = dynamic_cast<ViewDefinitionCP>(viewElement.get());
    if (!viewDef) {
        LOG.error("Element is not a ViewDefinition");
        return;
    }

    // 获取类别选择器
    BeInt64Id categorySelectorId = viewDef->GetCategorySelectorId();
    ElementPtr selectorElement = m_sourceDb.Elements().GetElement(categorySelectorId);
    if (!selectorElement.IsValid()) {
        LOG.errorv("Category selector not found: %s", categorySelectorId.ToString().c_str());
        return;
    }

    auto categorySelector = dynamic_cast<CategorySelectorCP>(selectorElement.get());
    if (!categorySelector) {
        LOG.error("Element is not a CategorySelector");
        return;
    }

    // 获取允许的类别列表
    bvector<BeInt64Id> categories = categorySelector->GetCategories();
    for (BeInt64Id categoryId : categories) {
        m_allowedCategories.insert(categoryId);
    }

    LOG.infov("Initialized view-based filter with %zu allowed categories",
              m_allowedCategories.size());
}

bool ViewBasedCategoryFilter::ShouldExportElement(ElementCR element) {
    auto geometricElement = dynamic_cast<GeometricElementCP>(&element);
    if (geometricElement != nullptr) {
        BeInt64Id categoryId = geometricElement->GetCategoryId();
        return m_allowedCategories.find(categoryId) != m_allowedCategories.end();
    }

    // 非几何元素默认导出
    return true;
}

void ViewBasedCategoryFilter::OnExportElement(ElementCR element, bool isUpdate) {
    LOG.tracev("Exporting element: %s", element.GetElementId().ToString().c_str());
}

CustomCategoryFilter::CustomCategoryFilter(std::function<bool(BeInt64Id)> categoryFilter)
    : m_categoryFilter(categoryFilter) {
}

bool CustomCategoryFilter::ShouldExportElement(ElementCR element) {
    auto geometricElement = dynamic_cast<GeometricElementCP>(&element);
    if (geometricElement != nullptr) {
        BeInt64Id categoryId = geometricElement->GetCategoryId();

        // 首先检查允许列表
        if (!m_allowedCategories.empty()) {
            return m_allowedCategories.find(categoryId) != m_allowedCategories.end();
        }

        // 然后使用自定义过滤器
        if (m_categoryFilter) {
            return m_categoryFilter(categoryId);
        }
    }

    return true;
}

void CustomCategoryFilter::AddAllowedCategory(BeInt64Id categoryId) {
    m_allowedCategories.insert(categoryId);
}

void CustomCategoryFilter::RemoveAllowedCategory(BeInt64Id categoryId) {
    m_allowedCategories.erase(categoryId);
}

} // namespace iModel
} // namespace BentleyApi
```

### 4. 使用示例

```cpp
// ExampleUsage.cpp
#include "IModelExporter.h"
#include "CategoryFilterTransformer.h"

namespace BentleyApi {
namespace iModel {

// 示例1：基本类别排除
void ExampleBasicCategoryExclusion() {
    // 打开源数据库
    IModelDbPtr sourceDb = IModelDb::OpenIModelDb("source.bim", IModelDb::OpenMode::Readonly);
    if (!sourceDb.IsValid()) {
        LOG.error("Failed to open source database");
        return;
    }

    // 创建导出器
    IModelExporter exporter(*sourceDb);

    // 排除特定类别
    BeInt64Id unwantedCategoryId = BeInt64Id::FromString("0x123456789abcdef0");
    exporter.ExcludeElementsInCategory(unwantedCategoryId);

    // 注册默认处理器
    auto handler = std::make_unique<IModelExportHandler>();
    exporter.RegisterHandler(std::move(handler));

    // 执行导出
    BentleyStatus status = exporter.ExportAll();
    if (SUCCESS != status) {
        LOG.error("Export failed");
    }
}

// 示例2：基于视图的过滤
void ExampleViewBasedFiltering() {
    IModelDbPtr sourceDb = IModelDb::OpenIModelDb("source.bim", IModelDb::OpenMode::Readonly);
    if (!sourceDb.IsValid()) {
        return;
    }

    IModelExporter exporter(*sourceDb);

    // 使用基于视图的过滤器
    BeInt64Id viewDefinitionId = BeInt64Id::FromString("0x987654321");
    auto handler = std::make_unique<ViewBasedCategoryFilter>(*sourceDb, viewDefinitionId);
    exporter.RegisterHandler(std::move(handler));

    BentleyStatus status = exporter.ExportAll();
    if (SUCCESS != status) {
        LOG.error("View-based export failed");
    }
}

// 示例3：自定义类别过滤
void ExampleCustomCategoryFiltering() {
    IModelDbPtr sourceDb = IModelDb::OpenIModelDb("source.bim", IModelDb::OpenMode::Readonly);
    if (!sourceDb.IsValid()) {
        return;
    }

    IModelExporter exporter(*sourceDb);

    // 创建自定义过滤器
    auto categoryFilter = [](BeInt64Id categoryId) -> bool {
        // 自定义逻辑：只导出特定范围的类别 ID
        uint64_t id = categoryId.GetValue();
        return id >= 0x1000 && id <= 0x2000;
    };

    auto handler = std::make_unique<CustomCategoryFilter>(categoryFilter);

    // 也可以直接添加允许的类别
    handler->AddAllowedCategory(BeInt64Id::FromString("0x123"));
    handler->AddAllowedCategory(BeInt64Id::FromString("0x456"));

    exporter.RegisterHandler(std::move(handler));

    BentleyStatus status = exporter.ExportAll();
    if (SUCCESS != status) {
        LOG.error("Custom filtering export failed");
    }
}

// 示例4：批量类别分析和过滤
class CategoryAnalyzer {
public:
    explicit CategoryAnalyzer(IModelDbR sourceDb) : m_sourceDb(sourceDb) {}

    std::unordered_map<BeInt64Id, size_t> AnalyzeCategoryUsage() {
        std::unordered_map<BeInt64Id, size_t> categoryUsage;

        Utf8String sql = R"(
            SELECT Category.Id, COUNT(*) as ElementCount
            FROM BisCore:GeometricElement
            GROUP BY Category.Id
        )";

        ECSqlStatement statement;
        if (ECSqlStatus::Success != statement.Prepare(m_sourceDb, sql.c_str())) {
            LOG.error("Failed to prepare category usage query");
            return categoryUsage;
        }

        while (BE_SQLITE_ROW == statement.Step()) {
            BeInt64Id categoryId = statement.GetValueId<BeInt64Id>(0);
            size_t elementCount = static_cast<size_t>(statement.GetValueInt64(1));
            categoryUsage[categoryId] = elementCount;
        }

        return categoryUsage;
    }

    std::vector<BeInt64Id> GetUnusedCategories() {
        std::unordered_set<BeInt64Id> allCategories;
        std::unordered_set<BeInt64Id> usedCategories;

        // 获取所有类别
        ECSqlStatement allCategoriesStmt;
        if (ECSqlStatus::Success == allCategoriesStmt.Prepare(m_sourceDb,
            "SELECT ECInstanceId FROM BisCore:SpatialCategory")) {
            while (BE_SQLITE_ROW == allCategoriesStmt.Step()) {
                allCategories.insert(allCategoriesStmt.GetValueId<BeInt64Id>(0));
            }
        }

        // 获取使用的类别
        ECSqlStatement usedCategoriesStmt;
        if (ECSqlStatus::Success == usedCategoriesStmt.Prepare(m_sourceDb,
            "SELECT DISTINCT Category.Id FROM BisCore:GeometricElement")) {
            while (BE_SQLITE_ROW == usedCategoriesStmt.Step()) {
                usedCategories.insert(usedCategoriesStmt.GetValueId<BeInt64Id>(0));
            }
        }

        // 返回未使用的类别
        std::vector<BeInt64Id> unusedCategories;
        for (BeInt64Id categoryId : allCategories) {
            if (usedCategories.find(categoryId) == usedCategories.end()) {
                unusedCategories.push_back(categoryId);
            }
        }

        return unusedCategories;
    }

    void ExcludeUnusedCategories(IModelExporter& exporter) {
        auto unusedCategories = GetUnusedCategories();
        for (BeInt64Id categoryId : unusedCategories) {
            exporter.ExcludeElementsInCategory(categoryId);
        }

        LOG.infov("Excluded %zu unused categories", unusedCategories.size());
    }

private:
    IModelDbR m_sourceDb;
};

void ExampleCategoryAnalysis() {
    IModelDbPtr sourceDb = IModelDb::OpenIModelDb("source.bim", IModelDb::OpenMode::Readonly);
    if (!sourceDb.IsValid()) {
        return;
    }

    // 分析类别使用情况
    CategoryAnalyzer analyzer(*sourceDb);
    auto usage = analyzer.AnalyzeCategoryUsage();

    LOG.infov("Found %zu categories in use", usage.size());
    for (const auto& pair : usage) {
        LOG.infov("Category %s: %zu elements",
                 pair.first.ToString().c_str(), pair.second);
    }

    // 创建导出器并排除未使用的类别
    IModelExporter exporter(*sourceDb);
    analyzer.ExcludeUnusedCategories(exporter);

    auto handler = std::make_unique<IModelExportHandler>();
    exporter.RegisterHandler(std::move(handler));

    BentleyStatus status = exporter.ExportAll();
    if (SUCCESS != status) {
        LOG.error("Analysis-based export failed");
    }
}

} // namespace iModel
} // namespace BentleyApi
```

## 性能优化策略

### 1. 内存管理优化

```cpp
// 使用智能指针管理内存
class OptimizedIModelExporter : public IModelExporter {
public:
    explicit OptimizedIModelExporter(IModelDbR sourceDb) : IModelExporter(sourceDb) {
        // 预分配容器大小以减少重新分配
        m_excludedElementCategoryIds.reserve(1000);
        m_excludedElementIds.reserve(10000);
    }

    // 批量排除类别
    void ExcludeElementsInCategories(const std::vector<BeInt64Id>& categoryIds) {
        for (BeInt64Id categoryId : categoryIds) {
            m_excludedElementCategoryIds.insert(categoryId);
        }
    }

    // 使用缓存避免重复查询
    bool ShouldExportElementCached(BeInt64Id elementId) {
        auto it = m_elementFilterCache.find(elementId);
        if (it != m_elementFilterCache.end()) {
            return it->second;
        }

        ElementPtr element = m_sourceDb.Elements().GetElement(elementId);
        bool shouldExport = element.IsValid() && ShouldExportElement(*element);
        m_elementFilterCache[elementId] = shouldExport;
        return shouldExport;
    }

private:
    std::unordered_map<BeInt64Id, bool> m_elementFilterCache;
};
```

### 2. 数据库查询优化

```cpp
// 批量查询优化器
class BatchQueryOptimizer {
public:
    explicit BatchQueryOptimizer(IModelDbR sourceDb) : m_sourceDb(sourceDb) {}

    // 批量获取元素信息
    std::vector<ElementInfo> GetElementsBatch(const std::vector<BeInt64Id>& elementIds) {
        std::vector<ElementInfo> results;

        if (elementIds.empty()) {
            return results;
        }

        // 构建批量查询 SQL
        Utf8String sql = "SELECT ECInstanceId, ECClassId, Category.Id FROM BisCore:Element WHERE ECInstanceId IN (";
        for (size_t i = 0; i < elementIds.size(); ++i) {
            if (i > 0) sql.append(",");
            sql.append("?");
        }
        sql.append(")");

        ECSqlStatement statement;
        if (ECSqlStatus::Success != statement.Prepare(m_sourceDb, sql.c_str())) {
            LOG.error("Failed to prepare batch query");
            return results;
        }

        // 绑定参数
        for (size_t i = 0; i < elementIds.size(); ++i) {
            statement.BindId(static_cast<int>(i + 1), elementIds[i]);
        }

        // 执行查询
        while (BE_SQLITE_ROW == statement.Step()) {
            ElementInfo info;
            info.elementId = statement.GetValueId<BeInt64Id>(0);
            info.classId = statement.GetValueId<ECClassId>(1);
            if (!statement.IsValueNull(2)) {
                info.categoryId = statement.GetValueId<BeInt64Id>(2);
            }
            results.push_back(info);
        }

        return results;
    }

    // 批量检查类别过滤
    std::unordered_map<BeInt64Id, bool> BatchCheckCategoryFilter(
        const std::vector<BeInt64Id>& elementIds,
        const std::unordered_set<BeInt64Id>& excludedCategories) {

        std::unordered_map<BeInt64Id, bool> results;
        auto elementInfos = GetElementsBatch(elementIds);

        for (const auto& info : elementInfos) {
            bool shouldExport = true;
            if (info.categoryId.IsValid()) {
                shouldExport = excludedCategories.find(info.categoryId) == excludedCategories.end();
            }
            results[info.elementId] = shouldExport;
        }

        return results;
    }

private:
    struct ElementInfo {
        BeInt64Id elementId;
        ECClassId classId;
        BeInt64Id categoryId;
    };

    IModelDbR m_sourceDb;
};
```

### 3. 并行处理支持

```cpp
// 并行导出器（需要线程安全考虑）
class ParallelCategoryExporter {
public:
    explicit ParallelCategoryExporter(IModelDbR sourceDb, size_t threadCount = 4)
        : m_sourceDb(sourceDb), m_threadCount(threadCount) {}

    BentleyStatus ExportModelContentsParallel(BeInt64Id modelId) {
        // 获取所有顶级元素 ID
        auto elementIds = GetTopLevelElementIds(modelId);
        if (elementIds.empty()) {
            return SUCCESS;
        }

        // 分批处理
        size_t batchSize = elementIds.size() / m_threadCount;
        if (batchSize == 0) batchSize = 1;

        std::vector<std::future<BentleyStatus>> futures;

        for (size_t i = 0; i < elementIds.size(); i += batchSize) {
            size_t endIdx = std::min(i + batchSize, elementIds.size());
            std::vector<BeInt64Id> batch(elementIds.begin() + i, elementIds.begin() + endIdx);

            // 启动异步任务
            auto future = std::async(std::launch::async, [this, batch]() {
                return ProcessElementBatch(batch);
            });
            futures.push_back(std::move(future));
        }

        // 等待所有任务完成
        BentleyStatus overallStatus = SUCCESS;
        for (auto& future : futures) {
            BentleyStatus status = future.get();
            if (SUCCESS != status) {
                overallStatus = status;
            }
        }

        return overallStatus;
    }

private:
    std::vector<BeInt64Id> GetTopLevelElementIds(BeInt64Id modelId) {
        std::vector<BeInt64Id> elementIds;

        Utf8String sql = "SELECT ECInstanceId FROM BisCore:Element WHERE Parent.Id IS NULL AND Model.Id=? ORDER BY ECInstanceId";

        ECSqlStatement statement;
        if (ECSqlStatus::Success == statement.Prepare(m_sourceDb, sql.c_str())) {
            statement.BindId(1, modelId);
            while (BE_SQLITE_ROW == statement.Step()) {
                elementIds.push_back(statement.GetValueId<BeInt64Id>(0));
            }
        }

        return elementIds;
    }

    BentleyStatus ProcessElementBatch(const std::vector<BeInt64Id>& elementIds) {
        // 注意：这里需要确保线程安全
        // 实际实现中可能需要为每个线程创建独立的数据库连接
        for (BeInt64Id elementId : elementIds) {
            // 处理单个元素
            // 这里简化处理，实际需要完整的导出逻辑
            LOG.tracev("Processing element in thread: %s", elementId.ToString().c_str());
        }
        return SUCCESS;
    }

    IModelDbR m_sourceDb;
    size_t m_threadCount;
};
```

## 最佳实践和注意事项

### 1. 错误处理策略

```cpp
class RobustCategoryExporter : public IModelExporter {
public:
    explicit RobustCategoryExporter(IModelDbR sourceDb) : IModelExporter(sourceDb) {}

    BentleyStatus ExportWithErrorRecovery() {
        try {
            return ExportAll();
        } catch (const std::exception& e) {
            LOG.errorv("Export failed with exception: %s", e.what());
            return ERROR;
        } catch (...) {
            LOG.error("Export failed with unknown exception");
            return ERROR;
        }
    }

    // 重写以添加错误恢复
    BentleyStatus ExportElement(BeInt64Id elementId) override {
        try {
            return IModelExporter::ExportElement(elementId);
        } catch (const std::exception& e) {
            LOG.errorv("Failed to export element %s: %s",
                      elementId.ToString().c_str(), e.what());
            m_failedElements.insert(elementId);
            return SUCCESS; // 继续处理其他元素
        }
    }

    const std::unordered_set<BeInt64Id>& GetFailedElements() const {
        return m_failedElements;
    }

private:
    std::unordered_set<BeInt64Id> m_failedElements;
};
```

### 2. 配置管理

```cpp
// 导出配置类
struct ExportConfiguration {
    bool wantGeometry = true;
    bool visitElements = true;
    bool visitRelationships = true;
    bool wantTemplateModels = false;
    size_t batchSize = 1000;
    size_t threadCount = 4;
    bool enableCaching = true;
    bool enableParallelProcessing = false;

    // 类别过滤配置
    std::unordered_set<BeInt64Id> excludedCategories;
    std::unordered_set<BeInt64Id> includedCategories; // 如果非空，只导出这些类别

    // 日志配置
    bool enableDetailedLogging = false;
    bool enableProgressReporting = true;
};

class ConfigurableExporter : public IModelExporter {
public:
    explicit ConfigurableExporter(IModelDbR sourceDb, const ExportConfiguration& config)
        : IModelExporter(sourceDb), m_config(config) {

        // 应用配置
        SetWantGeometry(config.wantGeometry);
        SetVisitElements(config.visitElements);
        SetVisitRelationships(config.visitRelationships);

        // 应用类别过滤
        for (BeInt64Id categoryId : config.excludedCategories) {
            ExcludeElementsInCategory(categoryId);
        }
    }

    bool ShouldExportElement(ElementCR element) const override {
        // 首先应用基类过滤
        if (!IModelExporter::ShouldExportElement(element)) {
            return false;
        }

        // 如果指定了包含列表，检查是否在列表中
        if (!m_config.includedCategories.empty()) {
            auto geometricElement = dynamic_cast<GeometricElementCP>(&element);
            if (geometricElement != nullptr) {
                BeInt64Id categoryId = geometricElement->GetCategoryId();
                return m_config.includedCategories.find(categoryId) != m_config.includedCategories.end();
            }
        }

        return true;
    }

private:
    ExportConfiguration m_config;
};
```

### 3. 性能监控

```cpp
// 性能监控器
class ExportPerformanceMonitor {
public:
    void StartExport() {
        m_startTime = std::chrono::high_resolution_clock::now();
        m_elementCount = 0;
        m_modelCount = 0;
        m_skippedElementCount = 0;
    }

    void OnElementExported() {
        ++m_elementCount;
        if (m_elementCount % 1000 == 0) {
            ReportProgress();
        }
    }

    void OnElementSkipped() {
        ++m_skippedElementCount;
    }

    void OnModelExported() {
        ++m_modelCount;
    }

    void EndExport() {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_startTime);

        LOG.infov("Export completed in %lld ms", duration.count());
        LOG.infov("Exported %zu elements, %zu models", m_elementCount, m_modelCount);
        LOG.infov("Skipped %zu elements", m_skippedElementCount);

        if (duration.count() > 0) {
            double elementsPerSecond = (m_elementCount * 1000.0) / duration.count();
            LOG.infov("Performance: %.2f elements/second", elementsPerSecond);
        }
    }

private:
    void ReportProgress() {
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_startTime);

        LOG.infov("Progress: %zu elements exported in %lld ms", m_elementCount, elapsed.count());
    }

    std::chrono::high_resolution_clock::time_point m_startTime;
    size_t m_elementCount = 0;
    size_t m_modelCount = 0;
    size_t m_skippedElementCount = 0;
};
```

## 与 TypeScript 版本的对比

| 特性 | TypeScript 版本 | C++ 版本 |
|------|----------------|----------|
| **内存管理** | 自动垃圾回收 | 手动管理，使用智能指针 |
| **类型安全** | 编译时检查 | 编译时检查 + 运行时检查 |
| **性能** | 中等（V8 优化） | 高（原生代码） |
| **开发效率** | 高（动态语言特性） | 中等（需要更多样板代码） |
| **错误处理** | 异常 + Promise | 异常 + 状态码 |
| **并发支持** | 单线程 + 异步 | 多线程 + 异步 |
| **部署** | 需要 Node.js 运行时 | 独立可执行文件 |

## 总结

iModelNative C++ 实现提供了更高的性能和更精细的控制，但需要更多的开发工作。主要优势包括：

1. **更高的性能**：原生代码执行，无需 JavaScript 引擎开销
2. **更好的内存控制**：精确的内存管理，避免垃圾回收暂停
3. **并行处理能力**：真正的多线程支持
4. **更小的部署包**：无需 Node.js 运行时

选择哪种实现方式取决于具体的需求：
- 如果需要最高性能和最小部署包，选择 C++ 实现
- 如果需要快速开发和易于维护，选择 TypeScript 实现
- 如果需要跨平台兼容性，TypeScript 实现更有优势
