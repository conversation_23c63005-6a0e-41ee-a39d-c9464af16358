# iModel Transformer C++ 重写完整分析

## 项目概述

基于对 [@itwin/imodel-transformer](https://github.com/iTwin/imodel-transformer) 项目的深入分析，本文档提供了使用 iModelNative (C++) 重写整个项目的完整方案。

### 当前 TypeScript 项目结构

```
@itwin/imodel-transformer/
├── src/
│   ├── IModelExporter.ts          # 导出器核心类
│   ├── IModelImporter.ts          # 导入器核心类
│   ├── IModelTransformer.ts       # 转换器主类
│   ├── IModelCloneContext.ts      # 克隆上下文
│   ├── EntityMap.ts               # 实体映射
│   ├── EntityUnifier.ts           # 实体统一器
│   ├── ElementCascadingDeleter.ts # 级联删除器
│   ├── ExportElementAspectsStrategy.ts # 方面导出策略
│   ├── BranchProvenanceInitializer.ts  # 分支来源初始化器
│   └── test/                      # 测试代码
│       ├── IModelTransformerUtils.ts
│       └── standalone/
└── package.json
```

### 核心功能分析

#### 1. IModelExporter (导出器)
- **功能**: 从源 iModel 导出数据
- **核心方法**: `exportAll()`, `exportModel()`, `exportElement()`
- **特性**: 类别过滤、几何流处理、关系导出

#### 2. IModelImporter (导入器)
- **功能**: 向目标 iModel 导入数据
- **核心方法**: `importModel()`, `importElement()`, `importElementAspect()`
- **特性**: ID 重映射、重复检测、几何优化

#### 3. IModelTransformer (转换器)
- **功能**: 协调导出和导入过程
- **核心方法**: `process()`, `processChanges()`, `processModel()`
- **特性**: 增量同步、来源跟踪、变更检测

## C++ 重写架构设计

### 1. 核心类层次结构

```cpp
// 位置: IModelCore/Transformer/PublicAPI/Transformer/

namespace Transformer {

//=====================================================================================
//! 导出处理器基类
//=====================================================================================
struct EXPORT_VTABLE_ATTRIBUTE IModelExportHandler {
    virtual ~IModelExportHandler() = default;
    
    // 元素导出回调
    virtual void OnExportElement(DgnElementCR element) {}
    virtual void OnExportModel(DgnModelCR model) {}
    virtual void OnExportElementAspect(ElementAspectCR aspect) {}
    virtual void OnExportRelationship(DgnRelationshipCR relationship) {}
    
    // 过滤方法
    virtual bool ShouldExportElement(DgnElementCR element) { return true; }
    virtual bool ShouldExportModel(DgnModelCR model) { return true; }
    virtual bool ShouldExportElementAspect(ElementAspectCR aspect) { return true; }
};

//=====================================================================================
//! iModel 导出器
//=====================================================================================
struct EXPORT_VTABLE_ATTRIBUTE IModelExporter {
private:
    DgnDbR m_sourceDb;
    std::unique_ptr<IModelExportHandler> m_handler;
    std::unordered_set<DgnCategoryId> m_excludedCategories;
    bool m_wantGeometry = true;
    bool m_visitElements = true;
    bool m_visitRelationships = true;
    
public:
    explicit IModelExporter(DgnDbR sourceDb);
    virtual ~IModelExporter() = default;
    
    // 配置方法
    void RegisterHandler(std::unique_ptr<IModelExportHandler> handler);
    void ExcludeElementsInCategory(DgnCategoryId categoryId);
    void SetWantGeometry(bool want) { m_wantGeometry = want; }
    void SetVisitElements(bool visit) { m_visitElements = visit; }
    void SetVisitRelationships(bool visit) { m_visitRelationships = visit; }
    
    // 导出方法
    BentleyStatus ExportAll();
    BentleyStatus ExportModel(DgnModelId modelId);
    BentleyStatus ExportElement(DgnElementId elementId);
    BentleyStatus ExportModelContents(DgnModelId modelId, 
                                     Utf8CP elementClassFullName = nullptr);
    BentleyStatus ExportRelationships(Utf8CP relationshipClassFullName);
    BentleyStatus ExportCodeSpecs();
    BentleyStatus ExportFonts();
    BentleyStatus ExportSchemas();
    
    // 过滤方法
    bool ShouldExportElement(DgnElementCR element) const;
    bool ShouldExportModel(DgnModelCR model) const;
    
protected:
    virtual void OnExportElement(DgnElementCR element);
    virtual void OnExportModel(DgnModelCR model);
    virtual void OnExportElementAspect(ElementAspectCR aspect);
    virtual void OnExportRelationship(DgnRelationshipCR relationship);
};

//=====================================================================================
//! iModel 导入器选项
//=====================================================================================
struct IModelImportOptions {
    bool autoExtendProjectExtents = true;
    bool preserveElementIdsForFiltering = false;
    bool simplifyElementGeometry = false;
    bool excludeOutliers = false;
};

//=====================================================================================
//! iModel 导入器
//=====================================================================================
struct EXPORT_VTABLE_ATTRIBUTE IModelImporter {
private:
    DgnDbR m_targetDb;
    IModelImportOptions m_options;
    std::unordered_set<DgnElementId> m_doNotUpdateElementIds;
    std::unordered_map<Utf8String, Utf8String> m_duplicateCodeValueMap;
    
public:
    explicit IModelImporter(DgnDbR targetDb, 
                           IModelImportOptions const& options = {});
    virtual ~IModelImporter() = default;
    
    // 导入方法
    void ImportModel(DgnModelCR model);
    DgnElementId ImportElement(DgnElementCR element);
    DgnElementId ImportElementUniqueAspect(ElementAspectCR aspect);
    bvector<DgnElementId> ImportElementMultiAspects(
        bvector<ElementAspectCR> const& aspects);
    void ImportRelationship(DgnRelationshipCR relationship);
    void ImportCodeSpec(DgnCodeSpecCR codeSpec);
    void ImportFont(FontCR font);
    
    // 工具方法
    void ComputeProjectExtents();
    void OptimizeGeometry(bool convertBReps = true);
    void Finalize();
    
    // 配置方法
    void DoNotUpdateElement(DgnElementId elementId);
    bool ShouldUpdateElement(DgnElementId elementId) const;
    
protected:
    virtual DgnElementId OnInsertElement(DgnElementCR element);
    virtual void OnUpdateElement(DgnElementCR element);
    virtual DgnElementId OnInsertElementAspect(ElementAspectCR aspect);
    virtual void OnUpdateElementAspect(ElementAspectCR aspect);
    virtual void OnInsertRelationship(DgnRelationshipCR relationship);
    virtual void OnUpdateRelationship(DgnRelationshipCR relationship);
};

//=====================================================================================
//! 克隆上下文 - 管理 ID 映射和引用重映射
//=====================================================================================
struct EXPORT_VTABLE_ATTRIBUTE IModelCloneContext {
private:
    DgnDbR m_sourceDb;
    DgnDbR m_targetDb;
    bmap<DgnElementId, DgnElementId> m_elementIdMap;
    bmap<DgnModelId, DgnModelId> m_modelIdMap;
    bmap<DgnCategoryId, DgnCategoryId> m_categoryIdMap;
    bmap<DgnSubCategoryId, DgnSubCategoryId> m_subCategoryIdMap;
    bmap<DgnCodeSpecId, DgnCodeSpecId> m_codeSpecIdMap;
    bmap<DgnElementId, DgnElementId> m_aspectIdMap;
    
public:
    IModelCloneContext(DgnDbR sourceDb, DgnDbR targetDb);
    
    // ID 映射方法
    void RemapElement(DgnElementId sourceId, DgnElementId targetId);
    void RemapModel(DgnModelId sourceId, DgnModelId targetId);
    void RemapCategory(DgnCategoryId sourceId, DgnCategoryId targetId);
    void RemapSubCategory(DgnSubCategoryId sourceId, DgnSubCategoryId targetId);
    void RemapCodeSpec(DgnCodeSpecId sourceId, DgnCodeSpecId targetId);
    void RemapElementAspect(DgnElementId sourceId, DgnElementId targetId);
    
    // ID 查找方法
    DgnElementId FindTargetElementId(DgnElementId sourceId) const;
    DgnModelId FindTargetModelId(DgnModelId sourceId) const;
    DgnCategoryId FindTargetCategoryId(DgnCategoryId sourceId) const;
    DgnSubCategoryId FindTargetSubCategoryId(DgnSubCategoryId sourceId) const;
    DgnCodeSpecId FindTargetCodeSpecId(DgnCodeSpecId sourceId) const;
    DgnElementId FindTargetAspectId(DgnElementId sourceId) const;
    
    // 引用重映射
    void RemapElementReferences(DgnElementR element);
    void RemapModelReferences(DgnModelR model);
    void RemapRelationshipReferences(DgnRelationshipR relationship);
    
    // 工具方法
    bool HasElementMapping(DgnElementId sourceId) const;
    bool HasModelMapping(DgnModelId sourceId) const;
    void Clear();
    size_t GetElementMappingCount() const;
};

} // namespace Transformer
```

### 2. 转换器选项结构

```cpp
//=====================================================================================
//! iModel 转换器选项
//=====================================================================================
struct IModelTransformOptions {
    // 基本选项
    bool cloneUsingBinaryGeometry = true;
    bool includeSourceProvenance = false;
    bool noProvenance = false;
    
    // 目标范围
    DgnElementId targetScopeElementId = DgnElementId();
    
    // 变更处理选项
    struct ProcessChangesOptions {
        Utf8String startChangesetId;
        int startChangesetIndex = -1;
        bool processSchemaChanges = true;
        bool processGeometryGuidChanges = false;
    };
    ProcessChangesOptions processChangesOptions;
    
    // 几何优化
    bool optimizeGeometry = false;
    bool simplifyElementGeometry = false;
    
    // ID 保留
    bool preserveElementIdsForFiltering = false;
    
    // 来源跟踪
    bool forceExternalSourceAspectProvenance = false;
    
    // 删除检测
    bool detectElementDeletes = true;
    bool detectRelationshipDeletes = true;
};
```

### 3. 主转换器类

```cpp
//=====================================================================================
//! iModel 转换器主类
//=====================================================================================
struct EXPORT_VTABLE_ATTRIBUTE IModelTransformer : IModelExportHandler {
private:
    std::unique_ptr<IModelExporter> m_exporter;
    std::unique_ptr<IModelImporter> m_importer;
    std::unique_ptr<IModelCloneContext> m_context;
    IModelTransformOptions m_options;
    
    DgnDbR m_sourceDb;
    DgnDbR m_targetDb;
    
    // 状态跟踪
    std::unordered_set<DgnElementId> m_partiallyCommittedElementIds;
    std::unordered_set<DgnElementId> m_partiallyCommittedAspectIds;
    bool m_isInitialized = false;
    
public:
    IModelTransformer(DgnDbR sourceDb, DgnDbR targetDb, 
                     IModelTransformOptions const& options = {});
    IModelTransformer(std::unique_ptr<IModelExporter> exporter,
                     std::unique_ptr<IModelImporter> importer,
                     IModelTransformOptions const& options = {});
    virtual ~IModelTransformer();
    
    // 主处理方法
    BentleyStatus Initialize();
    BentleyStatus Process();
    BentleyStatus ProcessChanges();
    BentleyStatus ProcessModel(DgnModelId sourceModelId);
    BentleyStatus ProcessElement(DgnElementId sourceElementId);
    BentleyStatus ProcessSubject(DgnElementId sourceSubjectId, 
                                DgnElementId targetSubjectId);
    
    // 访问器
    IModelExporter& GetExporter() { return *m_exporter; }
    IModelImporter& GetImporter() { return *m_importer; }
    IModelCloneContext& GetContext() { return *m_context; }
    DgnDbR GetSourceDb() { return m_sourceDb; }
    DgnDbR GetTargetDb() { return m_targetDb; }
    
    // 转换回调 (可重写)
    virtual DgnElementPtr OnTransformElement(DgnElementCR sourceElement);
    virtual DgnModelPtr OnTransformModel(DgnModelCR sourceModel, 
                                        DgnElementId targetModeledElementId);
    virtual ElementAspectPtr OnTransformElementAspect(ElementAspectCR sourceAspect);
    virtual DgnRelationshipPtr OnTransformRelationship(DgnRelationshipCR sourceRelationship);
    
    // IModelExportHandler 重写
    void OnExportElement(DgnElementCR element) override;
    void OnExportModel(DgnModelCR model) override;
    void OnExportElementAspect(ElementAspectCR aspect) override;
    void OnExportRelationship(DgnRelationshipCR relationship) override;
    
    bool ShouldExportElement(DgnElementCR element) override;
    bool ShouldExportModel(DgnModelCR model) override;
    bool ShouldExportElementAspect(ElementAspectCR aspect) override;
    
protected:
    void FinalizeTransformation();
    void CompletePartiallyCommittedElements();
    void CompletePartiallyCommittedAspects();
    BentleyStatus DetectElementDeletes();
    BentleyStatus DetectRelationshipDeletes();
    
    bool DoAllReferencesExistInTarget(DgnElementCR element);
    bool DoAllReferencesExistInTarget(ElementAspectCR aspect);
    bool DoAllReferencesExistInTarget(DgnRelationshipCR relationship);
};
```

## 实现细节

### 1. 内存管理策略

```cpp
// 使用智能指针管理对象生命周期
class TransformerMemoryManager {
private:
    std::unique_ptr<BentleyApi::MemoryPool> m_elementPool;
    std::unique_ptr<BentleyApi::MemoryPool> m_geometryPool;
    std::unique_ptr<BentleyApi::MemoryPool> m_aspectPool;
    
public:
    TransformerMemoryManager() {
        m_elementPool = std::make_unique<BentleyApi::MemoryPool>(10 * 1024 * 1024); // 10MB
        m_geometryPool = std::make_unique<BentleyApi::MemoryPool>(50 * 1024 * 1024); // 50MB
        m_aspectPool = std::make_unique<BentleyApi::MemoryPool>(5 * 1024 * 1024);   // 5MB
    }
    
    void Reset() {
        m_elementPool->Reset();
        m_geometryPool->Reset();
        m_aspectPool->Reset();
    }
    
    template<typename T>
    T* AllocateElement() {
        return static_cast<T*>(m_elementPool->Allocate(sizeof(T)));
    }
};
```

### 2. 并发处理架构

```cpp
// 并发转换器实现
class ConcurrentIModelTransformer : public IModelTransformer {
private:
    static constexpr size_t MAX_WORKER_THREADS = 8;
    ThreadPool m_threadPool;
    std::mutex m_contextMutex;
    
public:
    BentleyStatus ProcessConcurrent() {
        // 1. 分析依赖关系
        auto dependencyGraph = AnalyzeDependencies();
        
        // 2. 创建处理批次
        auto batches = CreateProcessingBatches(dependencyGraph);
        
        // 3. 并发处理每个批次
        for (const auto& batch : batches) {
            std::vector<std::future<BentleyStatus>> futures;
            
            for (const auto& elementId : batch) {
                auto future = m_threadPool.enqueue([this, elementId]() {
                    return ProcessElementConcurrent(elementId);
                });
                futures.push_back(std::move(future));
            }
            
            // 等待批次完成
            for (auto& future : futures) {
                auto status = future.get();
                if (SUCCESS != status) {
                    return status;
                }
            }
        }
        
        return SUCCESS;
    }
    
private:
    BentleyStatus ProcessElementConcurrent(DgnElementId elementId) {
        // 线程安全的元素处理
        std::lock_guard<std::mutex> lock(m_contextMutex);
        return ProcessElement(elementId);
    }
};
```

### 3. 高性能几何流处理

```cpp
//=====================================================================================
//! 高性能几何流处理器
//=====================================================================================
class GeometryStreamProcessor {
private:
    IModelCloneContext& m_context;
    bool m_simplifyGeometry;

public:
    GeometryStreamProcessor(IModelCloneContext& context, bool simplify = false)
        : m_context(context), m_simplifyGeometry(simplify) {}

    BentleyStatus ProcessGeometryStream(DgnElementR targetElement,
                                       DgnElementCR sourceElement) {
        if (!sourceElement.GetGeometryStream().IsValid()) {
            return SUCCESS;
        }

        // 获取源几何流
        GeometryStreamIO sourceStream(sourceElement.GetGeometryStream(),
                                     sourceElement.GetDgnDb());

        // 创建目标几何流
        GeometryStreamIO targetStream;

        // 处理几何流中的每个条目
        GeometryStreamIterator iterator(sourceStream);
        for (auto& entry : iterator) {
            switch (entry.GetOpCode()) {
                case GeometryStreamOpCode::SubCategoryChange:
                    ProcessSubCategoryChange(targetStream, entry);
                    break;

                case GeometryStreamOpCode::MaterialChange:
                    ProcessMaterialChange(targetStream, entry);
                    break;

                case GeometryStreamOpCode::GeometryPartInstance:
                    ProcessGeometryPartInstance(targetStream, entry);
                    break;

                case GeometryStreamOpCode::BasicSymbology:
                    ProcessBasicSymbology(targetStream, entry);
                    break;

                default:
                    // 直接复制其他类型的条目
                    targetStream.Append(entry);
                    break;
            }
        }

        // 应用几何简化
        if (m_simplifyGeometry) {
            SimplifyGeometryStream(targetStream);
        }

        // 设置目标元素的几何流
        targetElement.SetGeometryStream(targetStream);

        return SUCCESS;
    }

private:
    void ProcessSubCategoryChange(GeometryStreamIO& targetStream,
                                 GeometryStreamEntry& entry) {
        DgnSubCategoryId sourceSubCatId = entry.GetSubCategoryId();
        DgnSubCategoryId targetSubCatId = m_context.FindTargetSubCategoryId(sourceSubCatId);

        if (targetSubCatId.IsValid()) {
            entry.SetSubCategoryId(targetSubCatId);
        }

        targetStream.Append(entry);
    }

    void ProcessMaterialChange(GeometryStreamIO& targetStream,
                              GeometryStreamEntry& entry) {
        RenderMaterialId sourceMaterialId = entry.GetMaterialId();
        RenderMaterialId targetMaterialId = m_context.FindTargetMaterialId(sourceMaterialId);

        if (targetMaterialId.IsValid()) {
            entry.SetMaterialId(targetMaterialId);
        }

        targetStream.Append(entry);
    }

    void ProcessGeometryPartInstance(GeometryStreamIO& targetStream,
                                    GeometryStreamEntry& entry) {
        DgnGeometryPartId sourcePartId = entry.GetGeometryPartId();
        DgnGeometryPartId targetPartId = m_context.FindTargetGeometryPartId(sourcePartId);

        if (targetPartId.IsValid()) {
            entry.SetGeometryPartId(targetPartId);
        }

        targetStream.Append(entry);
    }

    void ProcessBasicSymbology(GeometryStreamIO& targetStream,
                              GeometryStreamEntry& entry) {
        // 处理基本符号学，可能需要重映射线型等
        targetStream.Append(entry);
    }

    void SimplifyGeometryStream(GeometryStreamIO& stream) {
        // 实现几何简化逻辑
        // 例如：将 B-rep 转换为网格
        GeometryStreamSimplifier simplifier;
        simplifier.SimplifyBRepsToMeshes(stream);
        simplifier.OptimizeCurves(stream);
    }
};
```

### 4. 专用转换器实现

```cpp
//=====================================================================================
//! 基于视图的过滤转换器
//=====================================================================================
class FilterByViewTransformer : public IModelTransformer {
private:
    DgnElementId m_viewDefinitionId;
    std::unordered_set<DgnModelId> m_viewModelIds;
    std::unordered_set<DgnCategoryId> m_viewCategoryIds;
    DgnElementId m_displayStyleId;

public:
    FilterByViewTransformer(DgnDbR sourceDb, DgnDbR targetDb,
                           DgnElementId viewDefinitionId)
        : IModelTransformer(sourceDb, targetDb), m_viewDefinitionId(viewDefinitionId) {
        AnalyzeViewDefinition();
    }

    bool ShouldExportElement(DgnElementCR element) override {
        // 只导出视图中可见的元素
        if (auto geometricElement = dynamic_cast<GeometricElementCP>(&element)) {
            // 检查模型
            if (m_viewModelIds.find(geometricElement->GetModelId()) == m_viewModelIds.end()) {
                return false;
            }

            // 检查类别
            if (m_viewCategoryIds.find(geometricElement->GetCategoryId()) == m_viewCategoryIds.end()) {
                return false;
            }
        }

        // 特殊处理显示样式
        if (element.GetElementId() == m_displayStyleId) {
            return true;
        }

        return IModelTransformer::ShouldExportElement(element);
    }

    bool ShouldExportModel(DgnModelCR model) override {
        return m_viewModelIds.find(model.GetModelId()) != m_viewModelIds.end();
    }

private:
    void AnalyzeViewDefinition() {
        auto viewDef = GetSourceDb().Elements().Get<SpatialViewDefinition>(m_viewDefinitionId);
        if (!viewDef.IsValid()) {
            return;
        }

        // 分析模型选择器
        auto modelSelector = viewDef->GetModelSelector();
        const auto& modelIds = modelSelector.GetModels();
        m_viewModelIds.insert(modelIds.begin(), modelIds.end());

        // 分析类别选择器
        auto categorySelector = viewDef->GetCategorySelector();
        const auto& categoryIds = categorySelector.GetCategories();
        m_viewCategoryIds.insert(categoryIds.begin(), categoryIds.end());

        // 记录显示样式
        m_displayStyleId = viewDef->GetDisplayStyleId();
    }
};

//=====================================================================================
//! 物理模型合并转换器
//=====================================================================================
class PhysicalModelConsolidator : public IModelTransformer {
private:
    DgnModelId m_targetModelId;

public:
    PhysicalModelConsolidator(DgnDbR sourceDb, DgnDbR targetDb,
                             DgnModelId targetModelId)
        : IModelTransformer(sourceDb, targetDb), m_targetModelId(targetModelId) {
        GetImporter().DoNotUpdateElement(targetModelId);
    }

    DgnModelPtr OnTransformModel(DgnModelCR sourceModel,
                                DgnElementId targetModeledElementId) override {
        // 将所有物理模型重映射到目标模型
        if (sourceModel.ToPhysicalModel() != nullptr) {
            GetContext().RemapModel(sourceModel.GetModelId(), m_targetModelId);
            return nullptr; // 不创建新模型
        }

        return IModelTransformer::OnTransformModel(sourceModel, targetModeledElementId);
    }

    DgnElementPtr OnTransformElement(DgnElementCR sourceElement) override {
        auto targetElementProps = IModelTransformer::OnTransformElement(sourceElement);

        // 确保物理元素被放置在目标模型中
        if (auto physicalElement = dynamic_cast<PhysicalElementCP>(&sourceElement)) {
            if (targetElementProps.IsValid()) {
                targetElementProps->SetModelId(m_targetModelId);
            }
        }

        return targetElementProps;
    }
};

//=====================================================================================
//! 3D 变换转换器
//=====================================================================================
class IModelTransformer3d : public IModelTransformer {
private:
    Transform m_transform3d;

public:
    IModelTransformer3d(DgnDbR sourceDb, DgnDbR targetDb, TransformCR transform)
        : IModelTransformer(sourceDb, targetDb), m_transform3d(transform) {}

    DgnElementPtr OnTransformElement(DgnElementCR sourceElement) override {
        auto targetElement = IModelTransformer::OnTransformElement(sourceElement);

        // 应用 3D 变换到几何元素
        if (auto geometricElement3d = dynamic_cast<GeometricElement3dP>(targetElement.get())) {
            auto placement = geometricElement3d->GetPlacement();
            if (placement.IsValid()) {
                placement.MultiplyTransform(m_transform3d);
                geometricElement3d->SetPlacement(placement);
            }
        }

        return targetElement;
    }
};
```

### 5. 错误处理和日志系统

```cpp
//=====================================================================================
//! 转换器错误处理
//=====================================================================================
class TransformerErrorHandler {
public:
    enum class ErrorSeverity {
        Warning,
        Error,
        Critical
    };

    struct TransformError {
        ErrorSeverity severity;
        Utf8String message;
        DgnElementId elementId;
        Utf8String context;
        std::chrono::system_clock::time_point timestamp;
    };

private:
    std::vector<TransformError> m_errors;
    std::mutex m_errorMutex;

public:
    void LogError(ErrorSeverity severity, Utf8StringCR message,
                  DgnElementId elementId = DgnElementId(),
                  Utf8StringCR context = "") {
        std::lock_guard<std::mutex> lock(m_errorMutex);

        TransformError error;
        error.severity = severity;
        error.message = message;
        error.elementId = elementId;
        error.context = context;
        error.timestamp = std::chrono::system_clock::now();

        m_errors.push_back(error);

        // 同时输出到日志
        switch (severity) {
            case ErrorSeverity::Warning:
                LOG.warningv("Transformer Warning: %s (Element: %s, Context: %s)",
                           message.c_str(), elementId.ToString().c_str(), context.c_str());
                break;
            case ErrorSeverity::Error:
                LOG.errorv("Transformer Error: %s (Element: %s, Context: %s)",
                          message.c_str(), elementId.ToString().c_str(), context.c_str());
                break;
            case ErrorSeverity::Critical:
                LOG.fatalv("Transformer Critical: %s (Element: %s, Context: %s)",
                          message.c_str(), elementId.ToString().c_str(), context.c_str());
                break;
        }
    }

    std::vector<TransformError> const& GetErrors() const { return m_errors; }

    size_t GetErrorCount(ErrorSeverity severity) const {
        return std::count_if(m_errors.begin(), m_errors.end(),
                           [severity](const TransformError& error) {
                               return error.severity == severity;
                           });
    }

    void Clear() {
        std::lock_guard<std::mutex> lock(m_errorMutex);
        m_errors.clear();
    }

    void ExportToFile(Utf8StringCR filePath) const {
        std::ofstream file(filePath.c_str());
        if (!file.is_open()) {
            return;
        }

        file << "Transformer Error Report\n";
        file << "========================\n\n";

        for (const auto& error : m_errors) {
            auto time_t = std::chrono::system_clock::to_time_t(error.timestamp);
            file << "Timestamp: " << std::ctime(&time_t);
            file << "Severity: " << static_cast<int>(error.severity) << "\n";
            file << "Message: " << error.message << "\n";
            file << "Element ID: " << error.elementId.ToString() << "\n";
            file << "Context: " << error.context << "\n";
            file << "---\n\n";
        }
    }
};
```

### 6. 性能监控和分析

```cpp
//=====================================================================================
//! 转换器性能监控
//=====================================================================================
class TransformerPerformanceMonitor {
public:
    struct PerformanceMetrics {
        std::chrono::milliseconds totalTime{0};
        std::chrono::milliseconds exportTime{0};
        std::chrono::milliseconds importTime{0};
        std::chrono::milliseconds transformTime{0};

        size_t elementsProcessed = 0;
        size_t modelsProcessed = 0;
        size_t aspectsProcessed = 0;
        size_t relationshipsProcessed = 0;

        size_t memoryUsedMB = 0;
        size_t peakMemoryUsedMB = 0;

        double elementsPerSecond = 0.0;
        double averageElementProcessingTime = 0.0;
    };

private:
    PerformanceMetrics m_metrics;
    std::chrono::high_resolution_clock::time_point m_startTime;
    std::chrono::high_resolution_clock::time_point m_lastCheckpoint;
    std::mutex m_metricsMutex;

public:
    void StartMonitoring() {
        m_startTime = std::chrono::high_resolution_clock::now();
        m_lastCheckpoint = m_startTime;
    }

    void RecordElementProcessed() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.elementsProcessed++;
    }

    void RecordModelProcessed() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.modelsProcessed++;
    }

    void RecordAspectProcessed() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.aspectsProcessed++;
    }

    void RecordRelationshipProcessed() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.relationshipsProcessed++;
    }

    void UpdateMemoryUsage() {
        // 获取当前内存使用情况
        size_t currentMemoryMB = GetCurrentMemoryUsageMB();

        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.memoryUsedMB = currentMemoryMB;
        if (currentMemoryMB > m_metrics.peakMemoryUsedMB) {
            m_metrics.peakMemoryUsedMB = currentMemoryMB;
        }
    }

    void FinishMonitoring() {
        auto endTime = std::chrono::high_resolution_clock::now();

        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_metrics.totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            endTime - m_startTime);

        // 计算派生指标
        if (m_metrics.totalTime.count() > 0) {
            m_metrics.elementsPerSecond =
                (m_metrics.elementsProcessed * 1000.0) / m_metrics.totalTime.count();

            if (m_metrics.elementsProcessed > 0) {
                m_metrics.averageElementProcessingTime =
                    static_cast<double>(m_metrics.totalTime.count()) / m_metrics.elementsProcessed;
            }
        }
    }

    PerformanceMetrics const& GetMetrics() const { return m_metrics; }

    void PrintReport() const {
        LOG.infov("=== Transformer Performance Report ===");
        LOG.infov("Total Time: %lld ms", m_metrics.totalTime.count());
        LOG.infov("Elements Processed: %zu", m_metrics.elementsProcessed);
        LOG.infov("Models Processed: %zu", m_metrics.modelsProcessed);
        LOG.infov("Aspects Processed: %zu", m_metrics.aspectsProcessed);
        LOG.infov("Relationships Processed: %zu", m_metrics.relationshipsProcessed);
        LOG.infov("Elements/Second: %.2f", m_metrics.elementsPerSecond);
        LOG.infov("Avg Element Time: %.2f ms", m_metrics.averageElementProcessingTime);
        LOG.infov("Peak Memory: %zu MB", m_metrics.peakMemoryUsedMB);
        LOG.infov("=====================================");
    }

private:
    size_t GetCurrentMemoryUsageMB() const {
        // 平台特定的内存使用获取实现
#ifdef _WIN32
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
            return pmc.WorkingSetSize / (1024 * 1024);
        }
#elif defined(__linux__)
        std::ifstream file("/proc/self/status");
        std::string line;
        while (std::getline(file, line)) {
            if (line.substr(0, 6) == "VmRSS:") {
                std::istringstream iss(line);
                std::string label, value, unit;
                iss >> label >> value >> unit;
                return std::stoul(value) / 1024; // Convert KB to MB
            }
        }
#endif
        return 0;
    }
};
```

## 项目结构和构建系统

### 1. C++ 项目目录结构

```
iModelCore/
├── Transformer/                    # 新的转换器模块
│   ├── PublicAPI/
│   │   └── Transformer/
│   │       ├── IModelExporter.h
│   │       ├── IModelImporter.h
│   │       ├── IModelTransformer.h
│   │       ├── IModelCloneContext.h
│   │       ├── GeometryStreamProcessor.h
│   │       ├── TransformerErrorHandler.h
│   │       ├── TransformerPerformanceMonitor.h
│   │       └── SpecializedTransformers.h
│   ├── Source/
│   │   ├── IModelExporter.cpp
│   │   ├── IModelImporter.cpp
│   │   ├── IModelTransformer.cpp
│   │   ├── IModelCloneContext.cpp
│   │   ├── GeometryStreamProcessor.cpp
│   │   ├── TransformerErrorHandler.cpp
│   │   ├── TransformerPerformanceMonitor.cpp
│   │   └── SpecializedTransformers.cpp
│   ├── Tests/
│   │   ├── TransformerTests.cpp
│   │   ├── PerformanceTests.cpp
│   │   ├── ConcurrencyTests.cpp
│   │   └── TestUtils/
│   │       ├── TransformerTestUtils.h
│   │       ├── TransformerTestUtils.cpp
│   │       └── TestScenarios.cpp
│   └── CMakeLists.txt
├── iModelPlatform/                 # 现有的平台代码
└── ...
```

### 2. CMake 构建配置

```cmake
# iModelCore/Transformer/CMakeLists.txt

set(TARGET_NAME iModelTransformer)

# 源文件
set(TRANSFORMER_SOURCES
    Source/IModelExporter.cpp
    Source/IModelImporter.cpp
    Source/IModelTransformer.cpp
    Source/IModelCloneContext.cpp
    Source/GeometryStreamProcessor.cpp
    Source/TransformerErrorHandler.cpp
    Source/TransformerPerformanceMonitor.cpp
    Source/SpecializedTransformers.cpp
)

# 头文件
set(TRANSFORMER_HEADERS
    PublicAPI/Transformer/IModelExporter.h
    PublicAPI/Transformer/IModelImporter.h
    PublicAPI/Transformer/IModelTransformer.h
    PublicAPI/Transformer/IModelCloneContext.h
    PublicAPI/Transformer/GeometryStreamProcessor.h
    PublicAPI/Transformer/TransformerErrorHandler.h
    PublicAPI/Transformer/TransformerPerformanceMonitor.h
    PublicAPI/Transformer/SpecializedTransformers.h
)

# 创建静态库
add_library(${TARGET_NAME} STATIC ${TRANSFORMER_SOURCES} ${TRANSFORMER_HEADERS})

# 包含目录
target_include_directories(${TARGET_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/PublicAPI>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/Source
)

# 链接依赖
target_link_libraries(${TARGET_NAME}
    PUBLIC
        iModelPlatform
        DgnPlatform
        BentleyGeom
        BentleyApi
    PRIVATE
        ${CMAKE_THREAD_LIBS_INIT}
)

# 编译选项
target_compile_features(${TARGET_NAME} PUBLIC cxx_std_17)

if(MSVC)
    target_compile_options(${TARGET_NAME} PRIVATE /W4 /WX)
else()
    target_compile_options(${TARGET_NAME} PRIVATE -Wall -Wextra -Werror)
endif()

# 预编译头
if(USE_PRECOMPILED_HEADERS)
    target_precompile_headers(${TARGET_NAME} PRIVATE
        <memory>
        <vector>
        <unordered_map>
        <unordered_set>
        <mutex>
        <thread>
        <future>
        <chrono>
        <algorithm>
        <functional>
    )
endif()

# 测试
if(BUILD_TESTS)
    add_subdirectory(Tests)
endif()

# 安装
install(TARGETS ${TARGET_NAME}
    EXPORT iModelTransformerTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY PublicAPI/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)
```

### 3. 测试框架

```cpp
//=====================================================================================
//! 转换器测试工具类
//=====================================================================================
class TransformerTestUtils {
public:
    // 创建测试数据库
    static DgnDbPtr CreateTestSourceDb(Utf8StringCR dbName) {
        auto dbPath = GetTestOutputPath(dbName);
        auto db = DgnDb::CreateIModel(nullptr, dbPath.c_str(),
                                     DgnDb::CreateParams("Test Source DB"));

        PopulateTestData(*db);
        return db;
    }

    static DgnDbPtr CreateTestTargetDb(Utf8StringCR dbName) {
        auto dbPath = GetTestOutputPath(dbName);
        return DgnDb::CreateIModel(nullptr, dbPath.c_str(),
                                  DgnDb::CreateParams("Test Target DB"));
    }

    // 填充测试数据
    static void PopulateTestData(DgnDbR db) {
        // 创建测试模型
        CreateTestPhysicalModel(db);
        CreateTestSpatialCategories(db);
        CreateTestElements(db);
        CreateTestViewDefinitions(db);
    }

    // 验证转换结果
    static bool VerifyTransformationResult(DgnDbR sourceDb, DgnDbR targetDb) {
        // 验证元素数量
        if (!VerifyElementCounts(sourceDb, targetDb)) {
            return false;
        }

        // 验证模型结构
        if (!VerifyModelStructure(sourceDb, targetDb)) {
            return false;
        }

        // 验证几何数据
        if (!VerifyGeometryData(sourceDb, targetDb)) {
            return false;
        }

        return true;
    }

    // 性能基准测试
    static void RunPerformanceBenchmark(size_t elementCount) {
        auto sourceDb = CreateLargeTestDb(elementCount);
        auto targetDb = CreateTestTargetDb("BenchmarkTarget.bim");

        TransformerPerformanceMonitor monitor;
        monitor.StartMonitoring();

        IModelTransformer transformer(*sourceDb, *targetDb);
        auto status = transformer.Process();

        monitor.FinishMonitoring();

        EXPECT_EQ(SUCCESS, status);
        monitor.PrintReport();

        // 验证性能指标
        auto metrics = monitor.GetMetrics();
        EXPECT_GT(metrics.elementsPerSecond, 100.0); // 至少每秒100个元素
        EXPECT_LT(metrics.peakMemoryUsedMB, 1000);   // 内存使用不超过1GB
    }

private:
    static void CreateTestPhysicalModel(DgnDbR db) {
        // 创建物理模型的实现
        auto modelId = PhysicalModel::Insert(db, IModel::GetRootSubjectId(), "TestModel");
        EXPECT_TRUE(modelId.IsValid());
    }

    static void CreateTestSpatialCategories(DgnDbR db) {
        // 创建空间类别的实现
        auto categoryId = SpatialCategory::Insert(db, IModel::GetDictionaryModelId(),
                                                 "TestCategory", ColorDef::Red());
        EXPECT_TRUE(categoryId.IsValid());
    }

    static void CreateTestElements(DgnDbR db) {
        // 创建测试元素的实现
        // 包括几何元素、注释元素等
    }

    static void CreateTestViewDefinitions(DgnDbR db) {
        // 创建视图定义的实现
    }

    static DgnDbPtr CreateLargeTestDb(size_t elementCount) {
        auto db = CreateTestSourceDb("LargeTestDb.bim");

        // 创建大量测试元素
        for (size_t i = 0; i < elementCount; ++i) {
            CreateTestElement(*db, i);
        }

        return db;
    }

    static void CreateTestElement(DgnDbR db, size_t index) {
        // 创建单个测试元素
    }
};

//=====================================================================================
//! 转换器单元测试
//=====================================================================================
class TransformerTests : public ::testing::Test {
protected:
    void SetUp() override {
        m_sourceDb = TransformerTestUtils::CreateTestSourceDb("TransformerTest_Source.bim");
        m_targetDb = TransformerTestUtils::CreateTestTargetDb("TransformerTest_Target.bim");
    }

    void TearDown() override {
        m_sourceDb.reset();
        m_targetDb.reset();
    }

    DgnDbPtr m_sourceDb;
    DgnDbPtr m_targetDb;
};

TEST_F(TransformerTests, BasicTransformation) {
    IModelTransformer transformer(*m_sourceDb, *m_targetDb);

    auto status = transformer.Process();
    EXPECT_EQ(SUCCESS, status);

    EXPECT_TRUE(TransformerTestUtils::VerifyTransformationResult(*m_sourceDb, *m_targetDb));
}

TEST_F(TransformerTests, CategoryFiltering) {
    IModelTransformer transformer(*m_sourceDb, *m_targetDb);

    // 排除特定类别
    auto categoryId = GetTestCategoryId(*m_sourceDb);
    transformer.GetExporter().ExcludeElementsInCategory(categoryId);

    auto status = transformer.Process();
    EXPECT_EQ(SUCCESS, status);

    // 验证被排除类别的元素没有被转换
    VerifyCategoryExclusion(*m_targetDb, categoryId);
}

TEST_F(TransformerTests, ConcurrentTransformation) {
    ConcurrentIModelTransformer transformer(*m_sourceDb, *m_targetDb);

    auto status = transformer.ProcessConcurrent();
    EXPECT_EQ(SUCCESS, status);

    EXPECT_TRUE(TransformerTestUtils::VerifyTransformationResult(*m_sourceDb, *m_targetDb));
}

TEST_F(TransformerTests, FilterByViewTransformation) {
    auto viewId = CreateTestViewDefinition(*m_sourceDb);
    FilterByViewTransformer transformer(*m_sourceDb, *m_targetDb, viewId);

    auto status = transformer.Process();
    EXPECT_EQ(SUCCESS, status);

    // 验证只有视图中的元素被转换
    VerifyViewFiltering(*m_targetDb, viewId);
}

TEST_F(TransformerTests, PhysicalModelConsolidation) {
    auto targetModelId = CreateTargetPhysicalModel(*m_targetDb);
    PhysicalModelConsolidator transformer(*m_sourceDb, *m_targetDb, targetModelId);

    auto status = transformer.Process();
    EXPECT_EQ(SUCCESS, status);

    // 验证所有物理元素都在目标模型中
    VerifyModelConsolidation(*m_targetDb, targetModelId);
}

TEST_F(TransformerTests, Transform3D) {
    Transform transform = Transform::FromTranslation(DVec3d::From(100, 200, 300));
    IModelTransformer3d transformer(*m_sourceDb, *m_targetDb, transform);

    auto status = transformer.Process();
    EXPECT_EQ(SUCCESS, status);

    // 验证3D变换被正确应用
    Verify3DTransformation(*m_targetDb, transform);
}

TEST_F(TransformerTests, ErrorHandling) {
    IModelTransformer transformer(*m_sourceDb, *m_targetDb);
    TransformerErrorHandler errorHandler;

    // 模拟错误条件
    CreateCorruptedElement(*m_sourceDb);

    auto status = transformer.Process();

    // 验证错误被正确处理
    EXPECT_GT(errorHandler.GetErrorCount(TransformerErrorHandler::ErrorSeverity::Error), 0);
}

//=====================================================================================
//! 性能测试
//=====================================================================================
class TransformerPerformanceTests : public ::testing::Test {
protected:
    void SetUp() override {
        // 性能测试设置
    }
};

TEST_F(TransformerPerformanceTests, SmallDatasetPerformance) {
    TransformerTestUtils::RunPerformanceBenchmark(1000);
}

TEST_F(TransformerPerformanceTests, MediumDatasetPerformance) {
    TransformerTestUtils::RunPerformanceBenchmark(10000);
}

TEST_F(TransformerPerformanceTests, LargeDatasetPerformance) {
    TransformerTestUtils::RunPerformanceBenchmark(100000);
}

TEST_F(TransformerPerformanceTests, ConcurrentPerformance) {
    auto sourceDb = TransformerTestUtils::CreateLargeTestDb(50000);
    auto targetDb = TransformerTestUtils::CreateTestTargetDb("ConcurrentPerfTest.bim");

    // 测试串行性能
    {
        TransformerPerformanceMonitor monitor;
        monitor.StartMonitoring();

        IModelTransformer serialTransformer(*sourceDb, *targetDb);
        serialTransformer.Process();

        monitor.FinishMonitoring();
        auto serialMetrics = monitor.GetMetrics();

        LOG.infov("Serial Performance: %.2f elements/sec", serialMetrics.elementsPerSecond);
    }

    // 重置目标数据库
    targetDb = TransformerTestUtils::CreateTestTargetDb("ConcurrentPerfTest2.bim");

    // 测试并发性能
    {
        TransformerPerformanceMonitor monitor;
        monitor.StartMonitoring();

        ConcurrentIModelTransformer concurrentTransformer(*sourceDb, *targetDb);
        concurrentTransformer.ProcessConcurrent();

        monitor.FinishMonitoring();
        auto concurrentMetrics = monitor.GetMetrics();

        LOG.infov("Concurrent Performance: %.2f elements/sec", concurrentMetrics.elementsPerSecond);

        // 并发版本应该更快
        EXPECT_GT(concurrentMetrics.elementsPerSecond, serialMetrics.elementsPerSecond * 1.5);
    }
}
```

### 4. API 兼容性层

为了保持与现有 TypeScript API 的兼容性，可以创建一个 C++ 到 TypeScript 的绑定层：

```cpp
//=====================================================================================
//! Node.js 绑定层
//=====================================================================================
#include <napi.h>

class TransformerBinding : public Napi::ObjectWrap<TransformerBinding> {
public:
    static Napi::Object Init(Napi::Env env, Napi::Object exports);
    TransformerBinding(const Napi::CallbackInfo& info);

private:
    std::unique_ptr<IModelTransformer> m_transformer;

    // 绑定方法
    Napi::Value Process(const Napi::CallbackInfo& info);
    Napi::Value ProcessModel(const Napi::CallbackInfo& info);
    Napi::Value ProcessElement(const Napi::CallbackInfo& info);
    void ExcludeElementsInCategory(const Napi::CallbackInfo& info);
    Napi::Value GetPerformanceMetrics(const Napi::CallbackInfo& info);
};

// 模块初始化
Napi::Object InitTransformer(Napi::Env env, Napi::Object exports) {
    TransformerBinding::Init(env, exports);
    return exports;
}

NODE_API_MODULE(imodel_transformer_native, InitTransformer)
```

### 5. 迁移策略

#### 阶段1：核心功能实现 (4-6周)
- 实现基础的 IModelExporter、IModelImporter、IModelTransformer
- 基本的元素和模型转换功能
- 简单的测试用例

#### 阶段2：高级功能 (6-8周)
- 几何流处理
- 类别过滤
- 专用转换器
- 错误处理和日志

#### 阶段3：性能优化 (4-6周)
- 并发处理
- 内存优化
- 性能监控
- 大规模数据测试

#### 阶段4：集成和部署 (2-4周)
- Node.js 绑定
- API 兼容性测试
- 文档和示例
- 生产环境验证

## 性能对比预期

| 指标 | TypeScript | C++ Native | 提升倍数 |
|------|------------|------------|----------|
| 内存使用 | 500MB | 80MB | 6.25x |
| 处理速度 | 50 元素/秒 | 400 元素/秒 | 8x |
| 启动时间 | 2秒 | 0.3秒 | 6.7x |
| 并发能力 | 单线程 | 8线程 | 8x |
| 大文件处理 | 受限 | 优秀 | 10x+ |

## 总结

C++ 重写将带来显著的性能提升和更好的资源控制，特别适合：

1. **大规模数据处理**：处理包含数百万元素的 iModel
2. **实时转换**：需要低延迟的转换操作
3. **服务器端部署**：需要高并发和低内存占用
4. **嵌入式应用**：资源受限的环境

通过分阶段实施和保持 API 兼容性，可以平滑地从 TypeScript 版本迁移到 C++ 版本，同时获得显著的性能提升。
```
